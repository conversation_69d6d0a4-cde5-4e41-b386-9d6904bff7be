package com.survivalunexpected.mod.items;

import com.survivalunexpected.mod.util.ChaosUtil;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.level.Level;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public class ChaosItems {

    public static class MysteryFoodItem extends Item {
        public MysteryFoodItem() {
            super(new Item.Properties()
                .food(new FoodProperties.Builder()
                    .alwaysEat()
                    .nutrition(4)
                    .saturationMod(0.3f)
                    .build())
                .stacksTo(16));
        }

        public ItemStack finishUsingItem(ItemStack stack, Level level, Player player) {
            if (!level.isClientSide && player instanceof ServerPlayer serverPlayer) {
                // Apply random effects when eaten
                boolean positive = ChaosUtil.shouldChaosOccur();
                MobEffectInstance effect1 = ChaosUtil.createRandomEffect(positive);
                MobEffectInstance effect2 = ChaosUtil.createRandomEffect(!positive);

                serverPlayer.addEffect(effect1);
                if (ChaosUtil.shouldChaosOccur()) {
                    serverPlayer.addEffect(effect2);
                }

                // Random hunger changes
                int hungerChange = ChaosUtil.getRandomCount(-3, 8);
                int newHunger = Math.max(0, Math.min(20, serverPlayer.getFoodData().getFoodLevel() + hungerChange));
                serverPlayer.getFoodData().setFoodLevel(newHunger);

                ChaosUtil.sendChaosMessage(serverPlayer, "The mystery food revealed its secrets! Culinary roulette!");

                // Chance to spawn random items
                if (ChaosUtil.shouldChaosOccur()) {
                    ItemStack randomItem = new ItemStack(ChaosUtil.getRandomItem(), ChaosUtil.getRandomCount(1, 3));
                    if (!serverPlayer.getInventory().add(randomItem)) {
                        serverPlayer.drop(randomItem, false);
                    }
                    ChaosUtil.sendChaosMessage(serverPlayer, "Your stomach produced a surprise! Digestive magic!");
                }
            }

            return super.finishUsingItem(stack, level, player);
        }

        @Override
        public void appendHoverText(ItemStack stack, @Nullable Level level, List<Component> tooltip, TooltipFlag flag) {
            tooltip.add(Component.literal("§dA mysterious food with unknown effects"));
            tooltip.add(Component.literal("§7Eating this could have any result..."));
            tooltip.add(Component.literal("§cWarning: Effects are completely random!"));
        }
    }

    public static class ChaosWandItem extends Item {
        public ChaosWandItem() {
            super(new Item.Properties()
                .stacksTo(1)
                .durability(100));
        }

        @Override
        public InteractionResultHolder<ItemStack> use(Level level, Player player, InteractionHand hand) {
            ItemStack stack = player.getItemInHand(hand);

            if (!level.isClientSide && player instanceof ServerPlayer serverPlayer) {
                // Apply random chaos effect to the player
                applyChaosWandEffect(serverPlayer, stack);

                // Damage the wand
                stack.hurtAndBreak(1, serverPlayer, (p) -> p.broadcastBreakEvent(hand));
            }

            return InteractionResultHolder.sidedSuccess(stack, level.isClientSide());
        }

        private void applyChaosWandEffect(ServerPlayer player, ItemStack wand) {
            int effectType = ChaosUtil.getRandomCount(1, 8);

            switch (effectType) {
                case 1:
                    // Teleport player randomly
                    double x = player.getX() + ChaosUtil.getRandomCount(-50, 50);
                    double z = player.getZ() + ChaosUtil.getRandomCount(-50, 50);
                    double y = player.level().getHeight() - 10;
                    player.teleportTo(x, y, z);
                    ChaosUtil.sendChaosMessage(player, "The wand teleported you! Spatial shuffle!");
                    break;

                case 2:
                    // Give random items
                    for (int i = 0; i < ChaosUtil.getRandomCount(3, 8); i++) {
                        ItemStack randomItem = new ItemStack(ChaosUtil.getRandomItem(), ChaosUtil.getRandomCount(1, 5));
                        if (!player.getInventory().add(randomItem)) {
                            player.drop(randomItem, false);
                        }
                    }
                    ChaosUtil.sendChaosMessage(player, "The wand conjured random items! Materialization magic!");
                    break;

                case 3:
                    // Apply multiple random effects
                    for (int i = 0; i < ChaosUtil.getRandomCount(2, 5); i++) {
                        boolean positive = ChaosUtil.shouldChaosOccur();
                        MobEffectInstance effect = ChaosUtil.createRandomEffect(positive);
                        player.addEffect(effect);
                    }
                    ChaosUtil.sendChaosMessage(player, "The wand enchanted you! Magical metamorphosis!");
                    break;

                case 4:
                    // Change weather (if possible)
                    if (player.hasPermissions(2)) {
                        if (ChaosUtil.shouldChaosOccur()) {
                            player.getServer().getCommands().performPrefixedCommand(
                                player.createCommandSourceStack(), "weather thunder 1000");
                            ChaosUtil.sendChaosMessage(player, "The wand summoned a storm! Weather wizard!");
                        } else {
                            player.getServer().getCommands().performPrefixedCommand(
                                player.createCommandSourceStack(), "weather clear 1000");
                            ChaosUtil.sendChaosMessage(player, "The wand cleared the skies! Atmospheric authority!");
                        }
                    }
                    break;

                case 5:
                    // Heal or harm player
                    if (ChaosUtil.shouldChaosOccur()) {
                        player.heal(ChaosUtil.getRandomCount(5, 20));
                        ChaosUtil.sendChaosMessage(player, "The wand healed you! Restorative magic!");
                    } else {
                        player.hurt(player.damageSources().magic(), ChaosUtil.getRandomCount(1, 5));
                        ChaosUtil.sendChaosMessage(player, "The wand zapped you! Chaotic consequences!");
                    }
                    break;

                case 6:
                    // Change time
                    if (player.hasPermissions(2)) {
                        long newTime = ChaosUtil.shouldChaosOccur() ? 1000 : 13000; // Day or night
                        player.getServer().getCommands().performPrefixedCommand(
                            player.createCommandSourceStack(), "time set " + newTime);
                        ChaosUtil.sendChaosMessage(player, "The wand controlled time! Temporal manipulation!");
                    }
                    break;

                case 7:
                    // Duplicate or destroy items in inventory
                    if (ChaosUtil.shouldChaosOccur()) {
                        // Duplicate random item
                        for (int i = 0; i < player.getInventory().getContainerSize(); i++) {
                            ItemStack item = player.getInventory().getItem(i);
                            if (!item.isEmpty() && ChaosUtil.shouldChaosOccur()) {
                                ItemStack duplicate = item.copy();
                                duplicate.setCount(Math.min(duplicate.getMaxStackSize(), duplicate.getCount() * 2));
                                player.getInventory().setItem(i, duplicate);
                                ChaosUtil.sendChaosMessage(player, "The wand duplicated your items! Multiplication magic!");
                                break;
                            }
                        }
                    } else {
                        // Randomly remove items
                        for (int i = 0; i < player.getInventory().getContainerSize(); i++) {
                            ItemStack item = player.getInventory().getItem(i);
                            if (!item.isEmpty() && ChaosUtil.shouldChaosOccur()) {
                                item.shrink(ChaosUtil.getRandomCount(1, item.getCount()));
                                ChaosUtil.sendChaosMessage(player, "The wand consumed your items! Destructive magic!");
                                break;
                            }
                        }
                    }
                    break;

                case 8:
                    // Transform nearby blocks
                    for (int dx = -5; dx <= 5; dx++) {
                        for (int dy = -2; dy <= 2; dy++) {
                            for (int dz = -5; dz <= 5; dz++) {
                                if (ChaosUtil.shouldChaosOccur()) {
                                    var pos = player.blockPosition().offset(dx, dy, dz);
                                    var currentBlock = player.level().getBlockState(pos).getBlock();
                                    if (!currentBlock.defaultBlockState().isAir()) {
                                        // Simple block transformation
                                        if (currentBlock == net.minecraft.world.level.block.Blocks.STONE) {
                                            player.level().setBlock(pos, net.minecraft.world.level.block.Blocks.DIAMOND_BLOCK.defaultBlockState(), 3);
                                        } else if (currentBlock == net.minecraft.world.level.block.Blocks.DIRT) {
                                            player.level().setBlock(pos, net.minecraft.world.level.block.Blocks.EMERALD_BLOCK.defaultBlockState(), 3);
                                        } else {
                                            player.level().setBlock(pos, net.minecraft.world.level.block.Blocks.GOLD_BLOCK.defaultBlockState(), 3);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    ChaosUtil.sendChaosMessage(player, "The wand transformed the world around you! Reality revision!");
                    break;
            }
        }

        @Override
        public void appendHoverText(ItemStack stack, @Nullable Level level, List<Component> tooltip, TooltipFlag flag) {
            tooltip.add(Component.literal("§dA wand of pure chaos"));
            tooltip.add(Component.literal("§7Right-click to unleash random magic"));
            tooltip.add(Component.literal("§cWarning: Effects are completely unpredictable!"));
            tooltip.add(Component.literal("§6Durability: " + (stack.getMaxDamage() - stack.getDamageValue()) + "/" + stack.getMaxDamage()));
        }
    }
}
