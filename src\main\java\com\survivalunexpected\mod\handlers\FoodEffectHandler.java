package com.survivalunexpected.mod.handlers;

import com.survivalunexpected.mod.config.ModConfig;
import com.survivalunexpected.mod.util.ChaosUtil;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraftforge.event.entity.living.LivingEntityUseItemEvent;
import net.minecraftforge.event.entity.player.PlayerInteractEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.registries.ForgeRegistries;

import java.util.Arrays;
import java.util.List;

public class FoodEffectHandler {

    // Lists of traditionally good and bad foods
    private static final List<Item> GOOD_FOODS = Arrays.asList(
        Items.GOLDEN_APPLE, Items.ENCHANTED_GOLDEN_APPLE, Items.COOKED_BEEF,
        Items.COOKED_PORKCHOP, Items.COOKED_CHICKEN, Items.COOKED_SALMON,
        Items.BREAD, Items.CAKE, Items.PUMPKIN_PIE, Items.GOLDEN_CARROT,
        Items.COOKED_MUTTON, Items.BAKED_POTATO, Items.MUSHROOM_STEW,
        Items.RABBIT_STEW, Items.BEETROOT_SOUP, Items.HONEY_BOTTLE
    );

    private static final List<Item> BAD_FOODS = Arrays.asList(
        Items.ROTTEN_FLESH, Items.SPIDER_EYE, Items.POISONOUS_POTATO,
        Items.PUFFERFISH, Items.CHICKEN, Items.PORKCHOP,
        Items.BEEF, Items.MUTTON, Items.RABBIT, Items.SALMON,
        Items.COD, Items.TROPICAL_FISH
    );

    // Non-food items that become edible
    private static final List<Item> EDIBLE_NON_FOODS = Arrays.asList(
        Items.DIRT, Items.STONE, Items.OAK_PLANKS, Items.IRON_INGOT,
        Items.DIAMOND, Items.EMERALD, Items.COAL, Items.REDSTONE,
        Items.STICK, Items.COBBLESTONE, Items.SAND, Items.GRAVEL,
        Items.GLASS, Items.WHITE_WOOL, Items.LEATHER, Items.PAPER,
        Items.BOOK, Items.FEATHER, Items.STRING, Items.BONE
    );

    @SubscribeEvent
    public void onItemUseFinish(LivingEntityUseItemEvent.Finish event) {
        if (!ModConfig.enableFoodChaos) return;
        if (!(event.getEntity() instanceof ServerPlayer player)) return;
        if (!ChaosUtil.shouldChaosOccur()) return;

        ItemStack itemStack = event.getItem();
        Item item = itemStack.getItem();

        // Handle normal food chaos
        if (item.isEdible()) {
            handleFoodChaos(player, item);
        }
    }

    @SubscribeEvent
    public void onRightClickItem(PlayerInteractEvent.RightClickItem event) {
        if (!ModConfig.enableFoodChaos) return;
        if (!ModConfig.makeNonFoodEdible) return;
        if (!(event.getEntity() instanceof ServerPlayer player)) return;
        if (event.getLevel().isClientSide()) return;

        ItemStack itemStack = event.getItemStack();
        Item item = itemStack.getItem();

        // Make non-food items edible
        if (!item.isEdible() && EDIBLE_NON_FOODS.contains(item) && ChaosUtil.shouldChaosOccur()) {
            handleNonFoodEating(player, item);

            // Consume the item
            if (!player.isCreative()) {
                itemStack.shrink(1);
            }
        }
    }

    private void handleFoodChaos(ServerPlayer player, Item food) {
        if (ModConfig.reverseFoodEffects) {
            // Reverse the effects of good and bad foods
            if (GOOD_FOODS.contains(food)) {
                applyBadFoodEffects(player, food);
            } else if (BAD_FOODS.contains(food)) {
                applyGoodFoodEffects(player, food);
            } else {
                applyRandomFoodEffects(player, food);
            }
        } else {
            // Apply random effects to all foods
            applyRandomFoodEffects(player, food);
        }
    }

    private void applyBadFoodEffects(ServerPlayer player, Item food) {
        // Good foods now give bad effects
        MobEffectInstance badEffect = ChaosUtil.createRandomEffect(false);
        player.addEffect(badEffect);

        // Specific bad effects for specific good foods
        if (food == Items.GOLDEN_APPLE || food == Items.ENCHANTED_GOLDEN_APPLE) {
            player.addEffect(new MobEffectInstance(MobEffects.WITHER, 600, 1));
            player.addEffect(new MobEffectInstance(MobEffects.WEAKNESS, 1200, 2));
            ChaosUtil.sendChaosMessage(player, "The golden apple was cursed! Fool's gold!");
        } else if (food == Items.BREAD) {
            player.addEffect(new MobEffectInstance(MobEffects.HUNGER, 800, 2));
            ChaosUtil.sendChaosMessage(player, "The bread made you hungrier! Paradoxical pastry!");
        } else if (food == Items.COOKED_BEEF || food == Items.COOKED_PORKCHOP) {
            player.addEffect(new MobEffectInstance(MobEffects.POISON, 400, 1));
            player.addEffect(new MobEffectInstance(MobEffects.CONFUSION, 600, 0));
            ChaosUtil.sendChaosMessage(player, "The cooked meat was actually raw inside! Deceptive dinner!");
        } else {
            ChaosUtil.sendChaosMessage(player, "Your favorite food betrayed you! Trust no meal!");
        }

        // Reduce hunger instead of filling it
        player.getFoodData().setFoodLevel(Math.max(0, player.getFoodData().getFoodLevel() - 3));
    }

    private void applyGoodFoodEffects(ServerPlayer player, Item food) {
        // Bad foods now give good effects
        MobEffectInstance goodEffect = ChaosUtil.createRandomEffect(true);
        player.addEffect(goodEffect);

        // Specific good effects for specific bad foods
        if (food == Items.ROTTEN_FLESH) {
            player.addEffect(new MobEffectInstance(MobEffects.REGENERATION, 600, 2));
            player.addEffect(new MobEffectInstance(MobEffects.DAMAGE_RESISTANCE, 1200, 1));
            ChaosUtil.sendChaosMessage(player, "Rotten flesh was aged to perfection! Zombie cuisine!");
        } else if (food == Items.SPIDER_EYE) {
            player.addEffect(new MobEffectInstance(MobEffects.NIGHT_VISION, 2400, 0));
            player.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SPEED, 1200, 2));
            ChaosUtil.sendChaosMessage(player, "Spider eye gave you arachnid abilities! Eight-legged vision!");
        } else if (food == Items.POISONOUS_POTATO) {
            player.addEffect(new MobEffectInstance(MobEffects.HEALTH_BOOST, 2400, 3));
            player.addEffect(new MobEffectInstance(MobEffects.SATURATION, 200, 5));
            ChaosUtil.sendChaosMessage(player, "Poisonous potato was actually a superfood! Toxic nutrition!");
        } else if (food == Items.PUFFERFISH) {
            player.addEffect(new MobEffectInstance(MobEffects.WATER_BREATHING, 3600, 0));
            player.addEffect(new MobEffectInstance(MobEffects.DOLPHINS_GRACE, 1200, 2));
            ChaosUtil.sendChaosMessage(player, "Pufferfish granted aquatic powers! Underwater mastery!");
        } else {
            ChaosUtil.sendChaosMessage(player, "Terrible food became a delicacy! Reverse psychology!");
        }

        // Fill hunger more than normal
        player.getFoodData().setFoodLevel(Math.min(20, player.getFoodData().getFoodLevel() + 6));
        player.getFoodData().setSaturation(player.getFoodData().getSaturationLevel() + 5.0f);
    }

    private void applyRandomFoodEffects(ServerPlayer player, Item food) {
        // Apply completely random effects
        boolean positive = ChaosUtil.shouldChaosOccur();
        MobEffectInstance randomEffect = ChaosUtil.createRandomEffect(positive);
        player.addEffect(randomEffect);

        // Chance for multiple effects
        if (ChaosUtil.shouldChaosOccur()) {
            MobEffectInstance secondEffect = ChaosUtil.createRandomEffect(!positive);
            player.addEffect(secondEffect);
        }

        // Random hunger changes
        int hungerChange = ChaosUtil.getRandomCount(-5, 8);
        int newHunger = Math.max(0, Math.min(20, player.getFoodData().getFoodLevel() + hungerChange));
        player.getFoodData().setFoodLevel(newHunger);

        String foodName = ForgeRegistries.ITEMS.getKey(food).getPath();
        ChaosUtil.sendChaosMessage(player, "The %s had mysterious effects! Food roulette!", foodName);
    }

    private void handleNonFoodEating(ServerPlayer player, Item item) {
        String itemName = ForgeRegistries.ITEMS.getKey(item).getPath();

        // Apply effects based on what non-food item was eaten
        if (item == Items.DIRT) {
            player.addEffect(new MobEffectInstance(MobEffects.DIG_SPEED, 1200, 2));
            player.addEffect(new MobEffectInstance(MobEffects.DAMAGE_RESISTANCE, 600, 1));
            ChaosUtil.sendChaosMessage(player, "Eating dirt made you one with the earth! Geological gastronomy!");
        } else if (item == Items.STONE) {
            player.addEffect(new MobEffectInstance(MobEffects.DAMAGE_RESISTANCE, 2400, 3));
            player.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SLOWDOWN, 600, 1));
            ChaosUtil.sendChaosMessage(player, "Stone diet made you rock-solid! Mineral meal!");
        } else if (item == Items.IRON_INGOT) {
            player.addEffect(new MobEffectInstance(MobEffects.DAMAGE_BOOST, 1200, 2));
            player.addEffect(new MobEffectInstance(MobEffects.FIRE_RESISTANCE, 1200, 0));
            ChaosUtil.sendChaosMessage(player, "Iron ingot strengthened your resolve! Metallic munchies!");
        } else if (item == Items.DIAMOND) {
            player.addEffect(new MobEffectInstance(MobEffects.LUCK, 2400, 3));
            player.addEffect(new MobEffectInstance(MobEffects.HEALTH_BOOST, 2400, 2));
            ChaosUtil.sendChaosMessage(player, "Diamond was surprisingly crunchy! Precious snack!");
        } else if (item == Items.COAL) {
            player.addEffect(new MobEffectInstance(MobEffects.NIGHT_VISION, 2400, 0));
            player.addEffect(new MobEffectInstance(MobEffects.FIRE_RESISTANCE, 1200, 0));
            ChaosUtil.sendChaosMessage(player, "Coal powered your inner fire! Combustible cuisine!");
        } else if (item == Items.REDSTONE) {
            player.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SPEED, 1200, 3));
            player.addEffect(new MobEffectInstance(MobEffects.JUMP, 1200, 2));
            ChaosUtil.sendChaosMessage(player, "Redstone energized your circuits! Electric eating!");
        } else {
            // Random effects for other non-food items
            boolean positive = ChaosUtil.shouldChaosOccur();
            MobEffectInstance randomEffect = ChaosUtil.createRandomEffect(positive);
            player.addEffect(randomEffect);
            ChaosUtil.sendChaosMessage(player, "You ate %s! Your stomach is confused!", itemName);
        }

        // Random hunger/saturation changes
        int hungerChange = ChaosUtil.getRandomCount(-3, 5);
        int newHunger = Math.max(0, Math.min(20, player.getFoodData().getFoodLevel() + hungerChange));
        player.getFoodData().setFoodLevel(newHunger);

        // Extreme chaos effects
        if (ModConfig.enableExtremeChaos && ChaosUtil.shouldChaosOccur()) {
            // Chance to spawn random items in inventory
            for (int i = 0; i < ChaosUtil.getRandomCount(1, 3); i++) {
                ItemStack randomItem = new ItemStack(ChaosUtil.getRandomItem(), ChaosUtil.getRandomCount(1, 4));
                if (!player.getInventory().add(randomItem)) {
                    player.drop(randomItem, false);
                }
            }
            ChaosUtil.sendChaosMessage(player, "Your stomach produced random items! Digestive alchemy!");
        }
    }
}
