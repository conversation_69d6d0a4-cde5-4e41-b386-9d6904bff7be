package com.survivalunexpected.mod.handlers;

import com.survivalunexpected.mod.config.ModConfig;
import com.survivalunexpected.mod.util.ChaosUtil;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.crafting.Recipe;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.registries.ForgeRegistries;

import java.util.Arrays;
import java.util.List;

public class CraftingChaosHandler {
    
    // Predefined chaos crafting results for specific items
    private static final List<Item> TOOL_CHAOS_RESULTS = Arrays.asList(
        Items.BREAD, Items.CAKE, Items.PUMPKIN_PIE, Items.COOKIE,
        Items.APPLE, Items.CARROT, Items.POTATO, Items.BEETROOT,
        Items.MELON_SLICE, Items.SWEET_BERRIES, Items.HONEY_BOTTLE
    );
    
    private static final List<Item> FOOD_CHAOS_RESULTS = Arrays.asList(
        Items.DIAMOND_SWORD, Items.IRON_PICKAXE, Items.GOLDEN_AXE,
        Items.STONE_SHOVEL, Items.WOODEN_HOE, Items.BOW,
        Items.CROSSBOW, Items.SHIELD, Items.FISHING_ROD
    );
    
    private static final List<Item> BLOCK_CHAOS_RESULTS = Arrays.asList(
        Items.ENDER_PEARL, Items.BLAZE_ROD, Items.GHAST_TEAR,
        Items.NETHER_STAR, Items.DRAGON_BREATH, Items.TOTEM_OF_UNDYING,
        Items.HEART_OF_THE_SEA, Items.NAUTILUS_SHELL, Items.PHANTOM_MEMBRANE
    );
    
    private static final List<Item> REDSTONE_CHAOS_RESULTS = Arrays.asList(
        Items.MUSIC_DISC_CAT, Items.MUSIC_DISC_BLOCKS, Items.MUSIC_DISC_CHIRP,
        Items.MUSIC_DISC_FAR, Items.MUSIC_DISC_MALL, Items.MUSIC_DISC_MELLOHI,
        Items.MUSIC_DISC_STAL, Items.MUSIC_DISC_STRAD, Items.MUSIC_DISC_WARD
    );
    
    @SubscribeEvent
    public void onItemCrafted(PlayerEvent.ItemCraftedEvent event) {
        if (!ModConfig.enableCraftingChaos) return;
        if (!(event.getEntity() instanceof ServerPlayer player)) return;
        if (!ChaosUtil.shouldChaosOccur()) return;
        
        ItemStack originalResult = event.getCrafting();
        Item originalItem = originalResult.getItem();
        
        if (ModConfig.randomizeAllRecipes) {
            // Completely randomize the crafting result
            ItemStack chaosResult = createChaosResult(originalItem, originalResult.getCount(), player);
            
            // Replace the crafted item
            event.getCrafting().setCount(0); // Clear original
            
            // Give the chaos result to the player
            if (!player.getInventory().add(chaosResult)) {
                player.drop(chaosResult, false);
            }
        }
    }
    
    private ItemStack createChaosResult(Item originalItem, int originalCount, ServerPlayer player) {
        Item chaosItem;
        int chaosCount = ChaosUtil.getRandomCount(1, Math.max(1, originalCount * 2));
        String originalName = ForgeRegistries.ITEMS.getKey(originalItem).getPath();
        
        // Determine chaos result based on original item type
        if (isTool(originalItem)) {
            chaosItem = getChaosResultFromList(TOOL_CHAOS_RESULTS);
            ChaosUtil.sendChaosMessage(player, "Your %s recipe created food instead! Culinary confusion!", originalName);
        } else if (isFood(originalItem)) {
            chaosItem = getChaosResultFromList(FOOD_CHAOS_RESULTS);
            ChaosUtil.sendChaosMessage(player, "Your %s recipe forged a tool! Kitchen weaponry!", originalName);
        } else if (isBlock(originalItem)) {
            chaosItem = getChaosResultFromList(BLOCK_CHAOS_RESULTS);
            ChaosUtil.sendChaosMessage(player, "Your %s recipe summoned rare materials! Architectural alchemy!", originalName);
        } else if (isRedstoneItem(originalItem)) {
            chaosItem = getChaosResultFromList(REDSTONE_CHAOS_RESULTS);
            ChaosUtil.sendChaosMessage(player, "Your %s recipe played music! Electronic entertainment!", originalName);
        } else if (isArmor(originalItem)) {
            chaosItem = getArmorChaosResult();
            ChaosUtil.sendChaosMessage(player, "Your %s recipe created something unexpected! Fashion failure!", originalName);
        } else if (isWeapon(originalItem)) {
            chaosItem = getWeaponChaosResult();
            ChaosUtil.sendChaosMessage(player, "Your %s recipe became peaceful! Swords to plowshares!", originalName);
        } else {
            // Generic chaos for other items
            chaosItem = getGenericChaosResult(originalItem);
            ChaosUtil.sendChaosMessage(player, "Your %s recipe defied all logic! Crafting chaos!", originalName);
        }
        
        // Extreme chaos modifications
        if (ModConfig.enableExtremeChaos) {
            return applyExtremeCraftingChaos(chaosItem, chaosCount, player);
        }
        
        return new ItemStack(chaosItem, chaosCount);
    }
    
    private Item getChaosResultFromList(List<Item> itemList) {
        return itemList.get(ChaosUtil.getRandomCount(0, itemList.size() - 1));
    }
    
    private Item getArmorChaosResult() {
        Item[] armorChaos = {
            Items.LEATHER_BOOTS, Items.CHAINMAIL_HELMET, Items.IRON_CHESTPLATE,
            Items.GOLDEN_LEGGINGS, Items.DIAMOND_HELMET, Items.NETHERITE_BOOTS,
            Items.TURTLE_HELMET, Items.ELYTRA, Items.CARVED_PUMPKIN
        };
        return armorChaos[ChaosUtil.getRandomCount(0, armorChaos.length - 1)];
    }
    
    private Item getWeaponChaosResult() {
        Item[] weaponChaos = {
            Items.FLOWER_POT, Items.PAINTING, Items.ITEM_FRAME,
            Items.BOOK, Items.PAPER, Items.FEATHER,
            Items.INK_SAC, Items.GLOW_INK_SAC, Items.WRITABLE_BOOK
        };
        return weaponChaos[ChaosUtil.getRandomCount(0, weaponChaos.length - 1)];
    }
    
    private Item getGenericChaosResult(Item originalItem) {
        // Create contextual chaos based on the original item
        String itemName = ForgeRegistries.ITEMS.getKey(originalItem).toString();
        
        if (itemName.contains("wood")) {
            return Items.STONE;
        } else if (itemName.contains("stone")) {
            return Items.IRON_INGOT;
        } else if (itemName.contains("iron")) {
            return Items.GOLD_INGOT;
        } else if (itemName.contains("gold")) {
            return Items.DIAMOND;
        } else if (itemName.contains("diamond")) {
            return Items.DIRT;
        } else {
            return ChaosUtil.getRandomItem();
        }
    }
    
    private ItemStack applyExtremeCraftingChaos(Item chaosItem, int chaosCount, ServerPlayer player) {
        // Extreme chaos can multiply results or change them completely
        if (ChaosUtil.shouldChaosOccur()) {
            // Multiply the result
            chaosCount *= ChaosUtil.getRandomCount(2, 5);
            ChaosUtil.sendChaosMessage(player, "EXTREME CHAOS! Your recipe multiplied!");
        }
        
        if (ChaosUtil.shouldChaosOccur()) {
            // Change to a completely random item
            chaosItem = ChaosUtil.getRandomItem();
            ChaosUtil.sendChaosMessage(player, "EXTREME CHAOS! Reality rewrote your recipe!");
        }
        
        if (ChaosUtil.shouldChaosOccur()) {
            // Create multiple different items
            for (int i = 0; i < ChaosUtil.getRandomCount(1, 3); i++) {
                ItemStack bonusItem = new ItemStack(ChaosUtil.getRandomItem(), ChaosUtil.getRandomCount(1, 4));
                if (!player.getInventory().add(bonusItem)) {
                    player.drop(bonusItem, false);
                }
            }
            ChaosUtil.sendChaosMessage(player, "EXTREME CHAOS! Bonus items materialized!");
        }
        
        return new ItemStack(chaosItem, Math.min(chaosCount, 64)); // Cap at stack size
    }
    
    // Helper methods to categorize items
    private boolean isTool(Item item) {
        String itemName = ForgeRegistries.ITEMS.getKey(item).getPath();
        return itemName.contains("pickaxe") || itemName.contains("axe") ||
               itemName.contains("shovel") || itemName.contains("hoe") ||
               itemName.contains("sword") || itemName.contains("bow") ||
               itemName.contains("crossbow") || itemName.contains("fishing_rod") ||
               itemName.contains("shears") || itemName.contains("flint_and_steel");
    }
    
    private boolean isFood(Item item) {
        return item.isEdible() || item == Items.CAKE || item == Items.MILK_BUCKET;
    }
    
    private boolean isBlock(Item item) {
        String itemName = ForgeRegistries.ITEMS.getKey(item).getPath();
        return itemName.contains("block") || itemName.contains("planks") ||
               itemName.contains("stone") || itemName.contains("brick") ||
               itemName.contains("log") || itemName.contains("wood");
    }
    
    private boolean isRedstoneItem(Item item) {
        String itemName = ForgeRegistries.ITEMS.getKey(item).getPath();
        return itemName.contains("redstone") || itemName.contains("repeater") ||
               itemName.contains("comparator") || itemName.contains("piston") ||
               itemName.contains("dispenser") || itemName.contains("dropper") ||
               itemName.contains("hopper") || itemName.contains("observer");
    }
    
    private boolean isArmor(Item item) {
        String itemName = ForgeRegistries.ITEMS.getKey(item).getPath();
        return itemName.contains("helmet") || itemName.contains("chestplate") ||
               itemName.contains("leggings") || itemName.contains("boots") ||
               item == Items.SHIELD || item == Items.ELYTRA;
    }
    
    private boolean isWeapon(Item item) {
        String itemName = ForgeRegistries.ITEMS.getKey(item).getPath();
        return itemName.contains("sword") || itemName.contains("bow") ||
               itemName.contains("crossbow") || itemName.contains("trident") ||
               item == Items.ARROW || item == Items.SPECTRAL_ARROW ||
               item == Items.TIPPED_ARROW;
    }
}
