package com.survivalunexpected.mod.config;

import com.survivalunexpected.mod.SurvivalUnexpectedMod;
import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.config.ModConfigEvent;

// Configuration class for Survival Unexpected mod
@Mod.EventBusSubscriber(modid = SurvivalUnexpectedMod.MODID, bus = Mod.EventBusSubscriber.Bus.MOD)
public class ModConfig
{
    private static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();

    // Block Breaking Chaos Configuration
    private static final ForgeConfigSpec.BooleanValue ENABLE_BLOCK_CHAOS = BUILDER
            .comment("Enable chaotic block breaking - blocks will drop unexpected items")
            .define("enableBlockChaos", true);

    private static final ForgeConfigSpec.IntValue CHAOS_SEED = BUILDER
            .comment("Seed for randomization (0 = random each time)")
            .defineInRange("chaosSeed", 0, 0, Integer.MAX_VALUE);

    private static final ForgeConfigSpec.DoubleValue CHAOS_CHANCE = BUILDER
            .comment("Chance for chaos to occur (0.0 = never, 1.0 = always)")
            .defineInRange("chaosChance", 0.8, 0.0, 1.0);

    // Mob Drop Chaos Configuration
    private static final ForgeConfigSpec.BooleanValue ENABLE_MOB_CHAOS = BUILDER
            .comment("Enable chaotic mob drops - mobs will drop unexpected items")
            .define("enableMobChaos", true);

    private static final ForgeConfigSpec.BooleanValue PEACEFUL_MOBS_DROP_DANGEROUS = BUILDER
            .comment("Peaceful mobs drop dangerous items")
            .define("peacefulMobsDropDangerous", true);

    private static final ForgeConfigSpec.BooleanValue HOSTILE_MOBS_DROP_HELPFUL = BUILDER
            .comment("Hostile mobs drop helpful items")
            .define("hostileMobsDropHelpful", true);

    // Crafting Chaos Configuration
    private static final ForgeConfigSpec.BooleanValue ENABLE_CRAFTING_CHAOS = BUILDER
            .comment("Enable chaotic crafting - recipes will be scrambled")
            .define("enableCraftingChaos", true);

    private static final ForgeConfigSpec.BooleanValue RANDOMIZE_ALL_RECIPES = BUILDER
            .comment("Completely randomize all crafting recipes")
            .define("randomizeAllRecipes", true);

    // Food Chaos Configuration
    private static final ForgeConfigSpec.BooleanValue ENABLE_FOOD_CHAOS = BUILDER
            .comment("Enable chaotic food effects - eating will have unexpected results")
            .define("enableFoodChaos", true);

    private static final ForgeConfigSpec.BooleanValue REVERSE_FOOD_EFFECTS = BUILDER
            .comment("Reverse food effects (good food becomes bad, bad food becomes good)")
            .define("reverseFoodEffects", true);

    private static final ForgeConfigSpec.BooleanValue MAKE_NON_FOOD_EDIBLE = BUILDER
            .comment("Make non-food items edible with random effects")
            .define("makeNonFoodEdible", true);

    // Tool Behavior Chaos Configuration
    private static final ForgeConfigSpec.BooleanValue ENABLE_TOOL_CHAOS = BUILDER
            .comment("Enable chaotic tool behavior - tools work on wrong materials")
            .define("enableToolChaos", true);

    private static final ForgeConfigSpec.BooleanValue REVERSE_TOOL_EFFECTIVENESS = BUILDER
            .comment("Reverse tool effectiveness (pickaxe works on wood, axe on stone, etc.)")
            .define("reverseToolEffectiveness", true);

    private static final ForgeConfigSpec.BooleanValue RANDOM_TOOL_DURABILITY = BUILDER
            .comment("Tools have random durability behavior")
            .define("randomToolDurability", true);

    // World Generation Chaos Configuration
    private static final ForgeConfigSpec.BooleanValue ENABLE_WORLD_CHAOS = BUILDER
            .comment("Enable world generation chaos - structures and ores spawn in unexpected places")
            .define("enableWorldChaos", true);

    private static final ForgeConfigSpec.BooleanValue SCRAMBLE_ORE_GENERATION = BUILDER
            .comment("Scramble ore generation patterns")
            .define("scrambleOreGeneration", true);

    // Advanced Chaos Configuration
    private static final ForgeConfigSpec.IntValue MAX_CHAOS_ITEMS = BUILDER
            .comment("Maximum number of items to drop from chaos events")
            .defineInRange("maxChaosItems", 5, 1, 64);

    private static final ForgeConfigSpec.BooleanValue ENABLE_CHAOS_MESSAGES = BUILDER
            .comment("Show chaos messages to players when unexpected things happen")
            .define("enableChaosMessages", true);

    private static final ForgeConfigSpec.BooleanValue ENABLE_EXTREME_CHAOS = BUILDER
            .comment("Enable extreme chaos mode - even more unpredictable behavior")
            .define("enableExtremeChaos", false);

    public static final ForgeConfigSpec SPEC = BUILDER.build();

    // Public static fields for easy access
    public static boolean enableBlockChaos;
    public static int chaosSeed;
    public static double chaosChance;
    public static boolean enableMobChaos;
    public static boolean peacefulMobsDropDangerous;
    public static boolean hostileMobsDropHelpful;
    public static boolean enableCraftingChaos;
    public static boolean randomizeAllRecipes;
    public static boolean enableFoodChaos;
    public static boolean reverseFoodEffects;
    public static boolean makeNonFoodEdible;
    public static boolean enableToolChaos;
    public static boolean reverseToolEffectiveness;
    public static boolean randomToolDurability;
    public static boolean enableWorldChaos;
    public static boolean scrambleOreGeneration;
    public static int maxChaosItems;
    public static boolean enableChaosMessages;
    public static boolean enableExtremeChaos;

    @SubscribeEvent
    static void onLoad(final ModConfigEvent event)
    {
        enableBlockChaos = ENABLE_BLOCK_CHAOS.get();
        chaosSeed = CHAOS_SEED.get();
        chaosChance = CHAOS_CHANCE.get();
        enableMobChaos = ENABLE_MOB_CHAOS.get();
        peacefulMobsDropDangerous = PEACEFUL_MOBS_DROP_DANGEROUS.get();
        hostileMobsDropHelpful = HOSTILE_MOBS_DROP_HELPFUL.get();
        enableCraftingChaos = ENABLE_CRAFTING_CHAOS.get();
        randomizeAllRecipes = RANDOMIZE_ALL_RECIPES.get();
        enableFoodChaos = ENABLE_FOOD_CHAOS.get();
        reverseFoodEffects = REVERSE_FOOD_EFFECTS.get();
        makeNonFoodEdible = MAKE_NON_FOOD_EDIBLE.get();
        enableToolChaos = ENABLE_TOOL_CHAOS.get();
        reverseToolEffectiveness = REVERSE_TOOL_EFFECTIVENESS.get();
        randomToolDurability = RANDOM_TOOL_DURABILITY.get();
        enableWorldChaos = ENABLE_WORLD_CHAOS.get();
        scrambleOreGeneration = SCRAMBLE_ORE_GENERATION.get();
        maxChaosItems = MAX_CHAOS_ITEMS.get();
        enableChaosMessages = ENABLE_CHAOS_MESSAGES.get();
        enableExtremeChaos = ENABLE_EXTREME_CHAOS.get();
    }
}
