package com.survivalunexpected.mod.handlers;

import com.survivalunexpected.mod.config.ModConfig;
import com.survivalunexpected.mod.util.ChaosUtil;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.*;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.event.level.BlockEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.registries.ForgeRegistries;

public class ToolBehaviorHandler {
    
    @SubscribeEvent
    public void onBlockBreakSpeed(PlayerEvent.BreakSpeed event) {
        if (!ModConfig.enableToolChaos) return;
        if (!ModConfig.reverseToolEffectiveness) return;
        if (!ChaosUtil.shouldChaosOccur()) return;
        
        if (event.getEntity() instanceof ServerPlayer player) {
            ItemStack heldItem = player.getMainHandItem();
            BlockState blockState = event.getState();
            
            if (heldItem.getItem() instanceof DiggerItem tool) {
                handleReversedToolEffectiveness(event, tool, blockState, player);
            }
        }
    }
    
    @SubscribeEvent
    public void onItemDurabilityChange(PlayerEvent.ItemCraftedEvent event) {
        if (!ModConfig.enableToolChaos) return;
        if (!ModConfig.randomToolDurability) return;
        if (!ChaosUtil.shouldChaosOccur()) return;
        
        ItemStack craftedItem = event.getCrafting();
        if (craftedItem.getItem() instanceof TieredItem tool && event.getEntity() instanceof ServerPlayer player) {
            handleRandomDurability(craftedItem, player);
        }
    }
    
    @SubscribeEvent
    public void onToolUse(BlockEvent.BreakEvent event) {
        if (!ModConfig.enableToolChaos) return;
        if (!(event.getPlayer() instanceof ServerPlayer player)) return;
        if (!ChaosUtil.shouldChaosOccur()) return;
        
        ItemStack heldItem = player.getMainHandItem();
        if (heldItem.getItem() instanceof TieredItem) {
            handleChaosToolEffects(event, heldItem, player);
        }
    }
    
    private void handleReversedToolEffectiveness(PlayerEvent.BreakSpeed event, DiggerItem tool, BlockState blockState, ServerPlayer player) {
        Block block = blockState.getBlock();
        String blockName = ForgeRegistries.BLOCKS.getKey(block).toString();
        
        // Reverse tool effectiveness
        if (tool instanceof PickaxeItem) {
            // Pickaxe works better on wood and leaves
            if (isWoodOrLeaves(block)) {
                event.setNewSpeed(event.getOriginalSpeed() * 5.0f);
                ChaosUtil.sendChaosMessage(player, "Your pickaxe loves wood! Mining redefined!");
            } else if (isStoneOrOre(block)) {
                event.setNewSpeed(event.getOriginalSpeed() * 0.2f);
                ChaosUtil.sendChaosMessage(player, "Your pickaxe refuses to mine stone! Tool rebellion!");
            }
        } else if (tool instanceof AxeItem) {
            // Axe works better on stone and ores
            if (isStoneOrOre(block)) {
                event.setNewSpeed(event.getOriginalSpeed() * 5.0f);
                ChaosUtil.sendChaosMessage(player, "Your axe became a geological tool! Stone chopper!");
            } else if (isWoodOrLeaves(block)) {
                event.setNewSpeed(event.getOriginalSpeed() * 0.2f);
                ChaosUtil.sendChaosMessage(player, "Your axe forgot how to chop wood! Identity crisis!");
            }
        } else if (tool instanceof ShovelItem) {
            // Shovel works better on hard materials
            if (isStoneOrOre(block) || block == Blocks.OBSIDIAN) {
                event.setNewSpeed(event.getOriginalSpeed() * 4.0f);
                ChaosUtil.sendChaosMessage(player, "Your shovel digs through stone like butter! Super spade!");
            } else if (isDirtOrSand(block)) {
                event.setNewSpeed(event.getOriginalSpeed() * 0.3f);
                ChaosUtil.sendChaosMessage(player, "Your shovel struggles with dirt! Backwards digging!");
            }
        } else if (tool instanceof HoeItem) {
            // Hoe works on everything except farmland
            if (block != Blocks.DIRT && block != Blocks.GRASS_BLOCK) {
                event.setNewSpeed(event.getOriginalSpeed() * 3.0f);
                ChaosUtil.sendChaosMessage(player, "Your hoe became a universal tool! Agricultural evolution!");
            }
        }
    }
    
    private void handleRandomDurability(ItemStack item, ServerPlayer player) {
        if (item.isDamageableItem()) {
            int maxDamage = item.getMaxDamage();
            int randomDurability = ChaosUtil.getRandomCount(1, maxDamage * 3); // Can be up to 3x normal durability
            
            // Create a new item with random durability
            item.setDamageValue(Math.max(0, maxDamage - randomDurability));
            
            if (randomDurability > maxDamage) {
                ChaosUtil.sendChaosMessage(player, "Your tool was blessed with extra durability! Crafting miracle!");
            } else if (randomDurability < maxDamage / 2) {
                ChaosUtil.sendChaosMessage(player, "Your tool was cursed with fragility! Handle with care!");
            } else {
                ChaosUtil.sendChaosMessage(player, "Your tool has mysterious durability! Quality unknown!");
            }
        }
    }
    
    private void handleChaosToolEffects(BlockEvent.BreakEvent event, ItemStack tool, ServerPlayer player) {
        Level level = (Level) event.getLevel();
        BlockPos pos = event.getPos();
        
        // Random tool effects when breaking blocks
        if (tool.getItem() instanceof SwordItem) {
            // Swords can break any block but with special effects
            if (ChaosUtil.shouldChaosOccur()) {
                // Sword creates explosion effect (visual only)
                createChaosEffect(level, pos, "explosion", player);
            }
        } else if (tool.getItem() instanceof BowItem) {
            // Bow can somehow break blocks
            if (ChaosUtil.shouldChaosOccur()) {
                createChaosEffect(level, pos, "arrow_rain", player);
            }
        } else if (tool.getItem() instanceof FishingRodItem) {
            // Fishing rod can catch blocks
            if (ChaosUtil.shouldChaosOccur()) {
                createChaosEffect(level, pos, "fishing", player);
            }
        }
        
        // Extreme chaos tool effects
        if (ModConfig.enableExtremeChaos) {
            handleExtremeToolChaos(event, tool, player);
        }
    }
    
    private void handleExtremeToolChaos(BlockEvent.BreakEvent event, ItemStack tool, ServerPlayer player) {
        Level level = (Level) event.getLevel();
        BlockPos pos = event.getPos();
        
        if (ChaosUtil.shouldChaosOccur()) {
            // Tools can break multiple blocks at once
            int radius = ChaosUtil.getRandomCount(1, 3);
            for (int x = -radius; x <= radius; x++) {
                for (int y = -radius; y <= radius; y++) {
                    for (int z = -radius; z <= radius; z++) {
                        if (x == 0 && y == 0 && z == 0) continue; // Skip center block
                        
                        BlockPos targetPos = pos.offset(x, y, z);
                        BlockState targetState = level.getBlockState(targetPos);
                        
                        if (!targetState.isAir() && ChaosUtil.shouldChaosOccur()) {
                            level.destroyBlock(targetPos, true);
                        }
                    }
                }
            }
            ChaosUtil.sendChaosMessage(player, "Your tool went berserk! Area destruction!");
        }
        
        if (ChaosUtil.shouldChaosOccur()) {
            // Tool transforms into a different tool
            Item[] tools = {
                Items.DIAMOND_PICKAXE, Items.DIAMOND_AXE, Items.DIAMOND_SHOVEL,
                Items.DIAMOND_HOE, Items.DIAMOND_SWORD, Items.BOW,
                Items.FISHING_ROD, Items.SHEARS, Items.FLINT_AND_STEEL
            };
            
            Item newTool = tools[ChaosUtil.getRandomCount(0, tools.length - 1)];
            ItemStack newToolStack = new ItemStack(newTool);
            
            // Copy enchantments if possible
            if (tool.isEnchanted()) {
                newToolStack.setTag(tool.getTag().copy());
            }
            
            player.setItemInHand(player.getUsedItemHand(), newToolStack);
            ChaosUtil.sendChaosMessage(player, "Your tool shapeshifted! Metamorphic mining!");
        }
    }
    
    private void createChaosEffect(Level level, BlockPos pos, String effectType, ServerPlayer player) {
        switch (effectType) {
            case "explosion":
                // Create visual explosion without damage
                level.explode(null, pos.getX(), pos.getY(), pos.getZ(), 0.0f, Level.ExplosionInteraction.NONE);
                ChaosUtil.sendChaosMessage(player, "Your sword exploded the block! Blade blast!");
                break;
                
            case "arrow_rain":
                // Spawn arrows around the area (as items)
                for (int i = 0; i < 5; i++) {
                    ChaosUtil.dropChaosItems(level, pos, Items.ARROW);
                }
                ChaosUtil.sendChaosMessage(player, "Your bow rained arrows! Archery anomaly!");
                break;
                
            case "fishing":
                // Spawn fish and water-related items
                ChaosUtil.dropChaosItems(level, pos, Items.COD, Items.SALMON, Items.TROPICAL_FISH, Items.KELP);
                ChaosUtil.sendChaosMessage(player, "Your fishing rod caught the block! Land fishing!");
                break;
        }
    }
    
    // Helper methods to identify block types
    private boolean isWoodOrLeaves(Block block) {
        String blockName = ForgeRegistries.BLOCKS.getKey(block).toString();
        return blockName.contains("log") || blockName.contains("wood") || 
               blockName.contains("planks") || blockName.contains("leaves") ||
               blockName.contains("stem");
    }
    
    private boolean isStoneOrOre(Block block) {
        String blockName = ForgeRegistries.BLOCKS.getKey(block).toString();
        return blockName.contains("stone") || blockName.contains("ore") ||
               blockName.contains("granite") || blockName.contains("diorite") ||
               blockName.contains("andesite") || blockName.contains("deepslate") ||
               blockName.contains("cobblestone");
    }
    
    private boolean isDirtOrSand(Block block) {
        return block == Blocks.DIRT || block == Blocks.GRASS_BLOCK ||
               block == Blocks.SAND || block == Blocks.RED_SAND ||
               block == Blocks.GRAVEL || block == Blocks.SOUL_SAND ||
               block == Blocks.SOUL_SOIL || block == Blocks.COARSE_DIRT;
    }
}
