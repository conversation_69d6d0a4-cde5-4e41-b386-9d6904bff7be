package com.survivalunexpected.mod.handlers;

import com.survivalunexpected.mod.config.ModConfig;
import com.survivalunexpected.mod.util.ChaosUtil;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraftforge.event.level.BlockEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.registries.ForgeRegistries;

import java.util.ArrayList;
import java.util.List;

public class BlockBreakHandler {
    
    @SubscribeEvent
    public void onBlockBreak(BlockEvent.BreakEvent event) {
        if (!ModConfig.enableBlockChaos) return;
        if (event.getLevel().isClientSide()) return;
        if (!ChaosUtil.shouldChaosOccur()) return;
        
        Level level = (Level) event.getLevel();
        BlockPos pos = event.getPos();
        Block brokenBlock = event.getState().getBlock();
        
        if (event.getPlayer() instanceof ServerPlayer player) {
            // Cancel normal drops
            event.setCanceled(true);
            
            // Remove the block manually
            level.setBlock(pos, Blocks.AIR.defaultBlockState(), 3);
            
            // Generate chaos drops based on what was broken
            generateChaosDrops(level, pos, brokenBlock, player);
        }
    }
    
    private void generateChaosDrops(Level level, BlockPos pos, Block brokenBlock, ServerPlayer player) {
        List<Item> chaosItems = new ArrayList<>();
        String blockName = ForgeRegistries.BLOCKS.getKey(brokenBlock).toString();
        
        // Determine chaos drops based on block type
        if (isWoodBlock(brokenBlock)) {
            handleWoodBlockChaos(chaosItems, player);
        } else if (isStoneBlock(brokenBlock)) {
            handleStoneBlockChaos(chaosItems, player);
        } else if (isOreBlock(brokenBlock)) {
            handleOreBlockChaos(chaosItems, player, brokenBlock);
        } else if (isDirtBlock(brokenBlock)) {
            handleDirtBlockChaos(chaosItems, player);
        } else if (isLeafBlock(brokenBlock)) {
            handleLeafBlockChaos(chaosItems, player);
        } else if (isSandBlock(brokenBlock)) {
            handleSandBlockChaos(chaosItems, player);
        } else {
            // Generic chaos for other blocks
            handleGenericBlockChaos(chaosItems, player);
        }
        
        // Drop the chaos items
        if (!chaosItems.isEmpty()) {
            ChaosUtil.dropChaosItems(level, pos, chaosItems.toArray(new Item[0]));
        }
        
        // Add some extreme chaos if enabled
        if (ModConfig.enableExtremeChaos) {
            addExtremeChaos(level, pos, player);
        }
    }
    
    private void handleWoodBlockChaos(List<Item> chaosItems, ServerPlayer player) {
        // Wood gives precious materials and food
        chaosItems.add(ChaosUtil.getWoodChaosItem());
        chaosItems.add(ChaosUtil.getWoodChaosItem());
        
        if (ChaosUtil.shouldChaosOccur()) {
            chaosItems.add(Items.ENCHANTED_GOLDEN_APPLE);
        }
        
        ChaosUtil.sendChaosMessage(player, "Wood turned into precious materials! Nature's magic!");
    }
    
    private void handleStoneBlockChaos(List<Item> chaosItems, ServerPlayer player) {
        // Stone gives plants and organic materials
        chaosItems.add(ChaosUtil.getStoneChaosItem());
        chaosItems.add(ChaosUtil.getStoneChaosItem());
        
        if (ChaosUtil.shouldChaosOccur()) {
            chaosItems.add(Items.BONE_MEAL);
            chaosItems.add(Items.BONE_MEAL);
        }
        
        ChaosUtil.sendChaosMessage(player, "Stone crumbled into life! The earth provides!");
    }
    
    private void handleOreBlockChaos(List<Item> chaosItems, ServerPlayer player, Block originalOre) {
        // Ores give building materials (opposite of their value)
        chaosItems.add(ChaosUtil.getOreChaosItem());
        chaosItems.add(ChaosUtil.getOreChaosItem());
        
        String oreName = ForgeRegistries.BLOCKS.getKey(originalOre).getPath();
        if (oreName.contains("diamond")) {
            chaosItems.add(Items.DIRT);
            chaosItems.add(Items.DIRT);
            ChaosUtil.sendChaosMessage(player, "Diamond ore turned to dirt! The ultimate irony!");
        } else if (oreName.contains("iron")) {
            chaosItems.add(Items.FLOWER_POT);
            chaosItems.add(Items.POPPY);
            ChaosUtil.sendChaosMessage(player, "Iron ore bloomed into flowers! Beauty over strength!");
        } else if (oreName.contains("gold")) {
            chaosItems.add(Items.COAL);
            chaosItems.add(Items.CHARCOAL);
            ChaosUtil.sendChaosMessage(player, "Gold ore turned to coal! From riches to fuel!");
        } else {
            ChaosUtil.sendChaosMessage(player, "Ore transformed into mundane materials!");
        }
    }
    
    private void handleDirtBlockChaos(List<Item> chaosItems, ServerPlayer player) {
        // Dirt gives rare and valuable items
        chaosItems.add(Items.DIAMOND);
        chaosItems.add(Items.EMERALD);
        chaosItems.add(Items.NETHERITE_SCRAP);
        
        ChaosUtil.sendChaosMessage(player, "Dirt revealed hidden treasures! The humble earth's secret!");
    }
    
    private void handleLeafBlockChaos(List<Item> chaosItems, ServerPlayer player) {
        // Leaves give tools and weapons
        chaosItems.add(Items.DIAMOND_PICKAXE);
        chaosItems.add(Items.IRON_SWORD);
        chaosItems.add(Items.BOW);
        
        ChaosUtil.sendChaosMessage(player, "Leaves forged into tools! Nature's workshop!");
    }
    
    private void handleSandBlockChaos(List<Item> chaosItems, ServerPlayer player) {
        // Sand gives water-related items
        chaosItems.add(Items.WATER_BUCKET);
        chaosItems.add(Items.FISHING_ROD);
        chaosItems.add(Items.TROPICAL_FISH);
        chaosItems.add(Items.KELP);
        
        ChaosUtil.sendChaosMessage(player, "Sand became the ocean! Desert turned to sea!");
    }
    
    private void handleGenericBlockChaos(List<Item> chaosItems, ServerPlayer player) {
        // Random chaos for other blocks
        chaosItems.add(ChaosUtil.getRandomItem());
        
        if (ChaosUtil.shouldChaosOccur()) {
            chaosItems.add(ChaosUtil.getRandomItem());
        }
        
        ChaosUtil.sendChaosMessage(player, "Reality shifted! Expect the unexpected!");
    }
    
    private void addExtremeChaos(Level level, BlockPos pos, ServerPlayer player) {
        // Extreme chaos adds even more unpredictable effects
        if (ChaosUtil.shouldChaosOccur()) {
            // Spawn random blocks nearby
            for (int i = 0; i < 3; i++) {
                BlockPos randomPos = pos.offset(
                    ChaosUtil.getRandomCount(-2, 2),
                    ChaosUtil.getRandomCount(-1, 1),
                    ChaosUtil.getRandomCount(-2, 2)
                );
                
                if (level.getBlockState(randomPos).isAir()) {
                    Block randomBlock = getRandomBlock();
                    level.setBlock(randomPos, randomBlock.defaultBlockState(), 3);
                }
            }
            
            ChaosUtil.sendChaosMessage(player, "EXTREME CHAOS! Reality is reshaping around you!");
        }
    }
    
    private Block getRandomBlock() {
        Block[] chaosBlocks = {
            Blocks.DIAMOND_BLOCK, Blocks.EMERALD_BLOCK, Blocks.GOLD_BLOCK,
            Blocks.IRON_BLOCK, Blocks.COAL_BLOCK, Blocks.REDSTONE_BLOCK,
            Blocks.LAPIS_BLOCK, Blocks.QUARTZ_BLOCK, Blocks.OBSIDIAN,
            Blocks.CRYING_OBSIDIAN, Blocks.ANCIENT_DEBRIS, Blocks.NETHERITE_BLOCK
        };
        return chaosBlocks[ChaosUtil.getRandomCount(0, chaosBlocks.length - 1)];
    }
    
    // Helper methods to identify block types
    private boolean isWoodBlock(Block block) {
        String blockName = ForgeRegistries.BLOCKS.getKey(block).toString();
        return blockName.contains("log") || blockName.contains("wood") || 
               blockName.contains("planks") || blockName.contains("stem");
    }
    
    private boolean isStoneBlock(Block block) {
        String blockName = ForgeRegistries.BLOCKS.getKey(block).toString();
        return blockName.contains("stone") || blockName.contains("cobblestone") ||
               blockName.contains("granite") || blockName.contains("diorite") ||
               blockName.contains("andesite") || blockName.contains("deepslate");
    }
    
    private boolean isOreBlock(Block block) {
        String blockName = ForgeRegistries.BLOCKS.getKey(block).toString();
        return blockName.contains("ore") || blockName.contains("_ore");
    }
    
    private boolean isDirtBlock(Block block) {
        return block == Blocks.DIRT || block == Blocks.GRASS_BLOCK || 
               block == Blocks.COARSE_DIRT || block == Blocks.PODZOL ||
               block == Blocks.MYCELIUM || block == Blocks.ROOTED_DIRT;
    }
    
    private boolean isLeafBlock(Block block) {
        String blockName = ForgeRegistries.BLOCKS.getKey(block).toString();
        return blockName.contains("leaves");
    }
    
    private boolean isSandBlock(Block block) {
        return block == Blocks.SAND || block == Blocks.RED_SAND || 
               block == Blocks.SOUL_SAND || block == Blocks.SOUL_SOIL;
    }
}
