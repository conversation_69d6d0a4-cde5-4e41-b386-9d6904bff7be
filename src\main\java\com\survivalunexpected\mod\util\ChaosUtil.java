package com.survivalunexpected.mod.util;

import com.survivalunexpected.mod.config.ModConfig;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.Level;
import net.minecraftforge.registries.ForgeRegistries;

import java.util.*;

public class ChaosUtil {
    private static final Random RANDOM = new Random();
    private static final List<Item> ALL_ITEMS = new ArrayList<>();
    private static final List<MobEffect> ALL_EFFECTS = new ArrayList<>();
    
    // Predefined chaos item pools for different scenarios
    private static final List<Item> WOOD_CHAOS_ITEMS = Arrays.asList(
        Items.DIAMOND, Items.EMERALD, Items.GOLD_INGOT, Items.IRON_INGOT,
        Items.COAL, Items.REDSTONE, Items.LAPIS_LAZULI, Items.QUARTZ,
        Items.BREAD, Items.COOKED_BEEF, Items.GOLDEN_APPLE
    );
    
    private static final List<Item> STONE_CHAOS_ITEMS = Arrays.asList(
        Items.WHEAT_SEEDS, Items.CARROT, Items.POTATO, Items.BEETROOT_SEEDS,
        Items.OAK_SAPLING, Items.BIRCH_SAPLING, Items.SPRUCE_SAPLING,
        Items.JUNGLE_SAPLING, Items.ACACIA_SAPLING, Items.DARK_OAK_SAPLING,
        Items.BAMBOO, Items.SUGAR_CANE, Items.CACTUS
    );
    
    private static final List<Item> ORE_CHAOS_ITEMS = Arrays.asList(
        Items.DIRT, Items.GRASS_BLOCK, Items.SAND, Items.GRAVEL,
        Items.COBBLESTONE, Items.MOSSY_COBBLESTONE, Items.STONE_BRICKS,
        Items.CRACKED_STONE_BRICKS, Items.CHISELED_STONE_BRICKS
    );
    
    private static final List<Item> PEACEFUL_MOB_DANGEROUS_DROPS = Arrays.asList(
        Items.TNT, Items.GUNPOWDER, Items.FIRE_CHARGE, Items.BLAZE_POWDER,
        Items.MAGMA_CREAM, Items.GHAST_TEAR, Items.WITHER_SKELETON_SKULL,
        Items.DRAGON_BREATH, Items.END_CRYSTAL, Items.TOTEM_OF_UNDYING
    );
    
    private static final List<Item> HOSTILE_MOB_HELPFUL_DROPS = Arrays.asList(
        Items.GOLDEN_APPLE, Items.ENCHANTED_GOLDEN_APPLE, Items.DIAMOND,
        Items.EMERALD, Items.NETHERITE_INGOT, Items.EXPERIENCE_BOTTLE,
        Items.BOOK, Items.ENCHANTED_BOOK, Items.ENDER_PEARL, Items.ELYTRA
    );
    
    private static final List<MobEffect> POSITIVE_EFFECTS = Arrays.asList(
        MobEffects.REGENERATION, MobEffects.DAMAGE_BOOST, MobEffects.MOVEMENT_SPEED,
        MobEffects.DIG_SPEED, MobEffects.JUMP, MobEffects.DAMAGE_RESISTANCE,
        MobEffects.FIRE_RESISTANCE, MobEffects.WATER_BREATHING, MobEffects.INVISIBILITY,
        MobEffects.NIGHT_VISION, MobEffects.HEALTH_BOOST, MobEffects.ABSORPTION,
        MobEffects.SATURATION, MobEffects.LUCK
    );
    
    private static final List<MobEffect> NEGATIVE_EFFECTS = Arrays.asList(
        MobEffects.POISON, MobEffects.WITHER, MobEffects.WEAKNESS,
        MobEffects.MOVEMENT_SLOWDOWN, MobEffects.DIG_SLOWDOWN, MobEffects.HARM,
        MobEffects.CONFUSION, MobEffects.BLINDNESS, MobEffects.HUNGER,
        MobEffects.UNLUCK, MobEffects.LEVITATION, MobEffects.GLOWING
    );
    
    static {
        initializeItemLists();
    }
    
    private static void initializeItemLists() {
        // Populate all items list
        ForgeRegistries.ITEMS.getValues().forEach(ALL_ITEMS::add);
        
        // Populate all effects list
        ForgeRegistries.MOB_EFFECTS.getValues().forEach(ALL_EFFECTS::add);
        
        // Set random seed if configured
        if (ModConfig.chaosSeed != 0) {
            RANDOM.setSeed(ModConfig.chaosSeed);
        }
    }
    
    public static boolean shouldChaosOccur() {
        return RANDOM.nextDouble() < ModConfig.chaosChance;
    }
    
    public static Item getRandomItem() {
        if (ALL_ITEMS.isEmpty()) return Items.DIRT;
        return ALL_ITEMS.get(RANDOM.nextInt(ALL_ITEMS.size()));
    }
    
    public static Item getWoodChaosItem() {
        return WOOD_CHAOS_ITEMS.get(RANDOM.nextInt(WOOD_CHAOS_ITEMS.size()));
    }
    
    public static Item getStoneChaosItem() {
        return STONE_CHAOS_ITEMS.get(RANDOM.nextInt(STONE_CHAOS_ITEMS.size()));
    }
    
    public static Item getOreChaosItem() {
        return ORE_CHAOS_ITEMS.get(RANDOM.nextInt(ORE_CHAOS_ITEMS.size()));
    }
    
    public static Item getPeacefulMobDangerousDrop() {
        return PEACEFUL_MOB_DANGEROUS_DROPS.get(RANDOM.nextInt(PEACEFUL_MOB_DANGEROUS_DROPS.size()));
    }
    
    public static Item getHostileMobHelpfulDrop() {
        return HOSTILE_MOB_HELPFUL_DROPS.get(RANDOM.nextInt(HOSTILE_MOB_HELPFUL_DROPS.size()));
    }
    
    public static MobEffect getRandomPositiveEffect() {
        return POSITIVE_EFFECTS.get(RANDOM.nextInt(POSITIVE_EFFECTS.size()));
    }
    
    public static MobEffect getRandomNegativeEffect() {
        return NEGATIVE_EFFECTS.get(RANDOM.nextInt(NEGATIVE_EFFECTS.size()));
    }
    
    public static MobEffectInstance createRandomEffect(boolean positive) {
        MobEffect effect = positive ? getRandomPositiveEffect() : getRandomNegativeEffect();
        int duration = 200 + RANDOM.nextInt(1800); // 10 seconds to 1.5 minutes
        int amplifier = RANDOM.nextInt(3); // 0-2 amplifier
        return new MobEffectInstance(effect, duration, amplifier);
    }
    
    public static void dropChaosItems(Level level, BlockPos pos, Item... items) {
        if (level.isClientSide) return;
        
        for (Item item : items) {
            int count = 1 + RANDOM.nextInt(ModConfig.maxChaosItems);
            ItemStack stack = new ItemStack(item, count);
            
            double x = pos.getX() + 0.5 + (RANDOM.nextDouble() - 0.5) * 0.7;
            double y = pos.getY() + 0.5;
            double z = pos.getZ() + 0.5 + (RANDOM.nextDouble() - 0.5) * 0.7;
            
            ItemEntity itemEntity = new ItemEntity(level, x, y, z, stack);
            itemEntity.setDefaultPickUpDelay();
            level.addFreshEntity(itemEntity);
        }
    }
    
    public static void sendChaosMessage(ServerPlayer player, String message) {
        if (ModConfig.enableChaosMessages) {
            player.sendSystemMessage(Component.literal("§d[CHAOS] §f" + message));
        }
    }
    
    public static void sendChaosMessage(ServerPlayer player, String message, Object... args) {
        if (ModConfig.enableChaosMessages) {
            player.sendSystemMessage(Component.literal("§d[CHAOS] §f" + String.format(message, args)));
        }
    }
    
    public static int getRandomCount(int min, int max) {
        return min + RANDOM.nextInt(max - min + 1);
    }
    
    public static boolean isWoodBlock(Item item) {
        String itemName = ForgeRegistries.ITEMS.getKey(item).toString();
        return itemName.contains("log") || itemName.contains("wood") || 
               itemName.contains("planks") || itemName.contains("stem");
    }
    
    public static boolean isStoneBlock(Item item) {
        String itemName = ForgeRegistries.ITEMS.getKey(item).toString();
        return itemName.contains("stone") || itemName.contains("cobblestone") ||
               itemName.contains("granite") || itemName.contains("diorite") ||
               itemName.contains("andesite") || itemName.contains("deepslate");
    }
    
    public static boolean isOreBlock(Item item) {
        String itemName = ForgeRegistries.ITEMS.getKey(item).toString();
        return itemName.contains("ore") || itemName.contains("_ore");
    }
    
    public static Item getContextualChaosItem(Item originalItem) {
        if (isWoodBlock(originalItem)) {
            return getWoodChaosItem();
        } else if (isStoneBlock(originalItem)) {
            return getStoneChaosItem();
        } else if (isOreBlock(originalItem)) {
            return getOreChaosItem();
        } else {
            return getRandomItem();
        }
    }
}
