{"block.survivalunexpected.chaos_block": "Chaos Block", "item.survivalunexpected.chaos_block": "Chaos Block", "item.survivalunexpected.mystery_food": "Mystery Food", "item.survivalunexpected.chaos_wand": "<PERSON> Wand", "itemGroup.survivalunexpected.chaos_tab": "Survival Unexpected", "tooltip.survivalunexpected.chaos_block": "A block that embodies pure chaos", "tooltip.survivalunexpected.mystery_food": "Food with completely random effects", "tooltip.survivalunexpected.chaos_wand": "A wand of unpredictable magic", "message.survivalunexpected.chaos.block_break": "Reality shifted as you broke that block!", "message.survivalunexpected.chaos.mob_drop": "That creature defied all expectations!", "message.survivalunexpected.chaos.food_effect": "Your meal had mysterious consequences!", "message.survivalunexpected.chaos.tool_behavior": "Your tool forgot how to work properly!", "message.survivalunexpected.chaos.crafting": "Your recipe was rewritten by chaos!", "message.survivalunexpected.chaos.world_change": "The world around you transformed!", "config.survivalunexpected.title": "Survival Unexpected Configuration", "config.survivalunexpected.enableBlockChaos": "Enable Block Breaking Chaos", "config.survivalunexpected.enableMobChaos": "Enable Mob Drop Chaos", "config.survivalunexpected.enableFoodChaos": "Enable Food Effect Chaos", "config.survivalunexpected.enableToolChaos": "Enable Tool Behavior Chaos", "config.survivalunexpected.enableCraftingChaos": "Enable Crafting Recipe Chaos", "config.survivalunexpected.enableWorldChaos": "Enable World Generation Chaos", "config.survivalunexpected.enableExtremeChaos": "Enable Extreme Chaos Mode", "config.survivalunexpected.chaosChance": "Chaos Occurrence Chance", "config.survivalunexpected.chaosSeed": "Chaos Randomization Seed", "config.survivalunexpected.maxChaosItems": "Maximum Chaos Items per Event", "config.survivalunexpected.enableChaosMessages": "Show Chaos Messages", "advancement.survivalunexpected.root.title": "Welcome to Chaos", "advancement.survivalunexpected.root.description": "Install the Survival Unexpected mod", "advancement.survivalunexpected.first_chaos.title": "First Taste of Chaos", "advancement.survivalunexpected.first_chaos.description": "Experience your first chaos event", "advancement.survivalunexpected.chaos_master.title": "Master of Chaos", "advancement.survivalunexpected.chaos_master.description": "Experience 100 chaos events", "advancement.survivalunexpected.extreme_chaos.title": "Extreme Chaos Survivor", "advancement.survivalunexpected.extreme_chaos.description": "Survive with extreme chaos mode enabled", "death.attack.survivalunexpected.chaos": "%1$s was consumed by chaos", "death.attack.survivalunexpected.chaos.player": "%1$s was driven mad by %2$s's chaos", "subtitle.survivalunexpected.chaos_event": "Chaos occurs", "subtitle.survivalunexpected.reality_shift": "Reality shifts", "subtitle.survivalunexpected.unexpected_result": "Something unexpected happens"}