package com.survivalunexpected.mod;

import com.mojang.logging.LogUtils;
import com.survivalunexpected.mod.config.ModConfig;
import com.survivalunexpected.mod.handlers.*;
import com.survivalunexpected.mod.items.ChaosItems;
import net.minecraft.client.Minecraft;
import net.minecraft.core.registries.Registries;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.CreativeModeTab;
import net.minecraft.world.item.CreativeModeTabs;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockBehaviour;
import net.minecraft.world.level.material.MapColor;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.BuildCreativeModeTabContentsEvent;
import net.minecraftforge.event.server.ServerStartingEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;
import org.slf4j.Logger;

// The value here should match an entry in the META-INF/mods.toml file
@Mod(SurvivalUnexpectedMod.MODID)
public class SurvivalUnexpectedMod
{
    // Define mod id in a common place for everything to reference
    public static final String MODID = "survivalunexpected";
    // Directly reference a slf4j logger
    private static final Logger LOGGER = LogUtils.getLogger();

    // Create a Deferred Register to hold Blocks which will all be registered under the "survivalunexpected" namespace
    public static final DeferredRegister<Block> BLOCKS = DeferredRegister.create(ForgeRegistries.BLOCKS, MODID);
    // Create a Deferred Register to hold Items which will all be registered under the "survivalunexpected" namespace
    public static final DeferredRegister<Item> ITEMS = DeferredRegister.create(ForgeRegistries.ITEMS, MODID);
    // Create a Deferred Register to hold CreativeModeTabs which will all be registered under the "survivalunexpected" namespace
    public static final DeferredRegister<CreativeModeTab> CREATIVE_MODE_TABS = DeferredRegister.create(Registries.CREATIVE_MODE_TAB, MODID);

    // Creates a chaos block that represents the unpredictable nature of this mod
    public static final RegistryObject<Block> CHAOS_BLOCK = BLOCKS.register("chaos_block",
        () -> new Block(BlockBehaviour.Properties.of().mapColor(MapColor.COLOR_PURPLE).strength(2.0F, 6.0F)));
    public static final RegistryObject<Item> CHAOS_BLOCK_ITEM = ITEMS.register("chaos_block",
        () -> new BlockItem(CHAOS_BLOCK.get(), new Item.Properties()));

    // Creates a mysterious food item that has random effects
    public static final RegistryObject<Item> MYSTERY_FOOD = ITEMS.register("mystery_food",
        ChaosItems.MysteryFoodItem::new);

    // Creates a chaos wand with random magical effects
    public static final RegistryObject<Item> CHAOS_WAND = ITEMS.register("chaos_wand",
        ChaosItems.ChaosWandItem::new);

    // Creates a creative tab for our chaotic items
    public static final RegistryObject<CreativeModeTab> CHAOS_TAB = CREATIVE_MODE_TABS.register("chaos_tab",
        () -> CreativeModeTab.builder()
            .withTabsBefore(CreativeModeTabs.COMBAT)
            .icon(() -> CHAOS_BLOCK_ITEM.get().getDefaultInstance())
            .displayItems((parameters, output) -> {
                output.accept(CHAOS_BLOCK_ITEM.get());
                output.accept(MYSTERY_FOOD.get());
                output.accept(CHAOS_WAND.get());
            }).build());

    public SurvivalUnexpectedMod(FMLJavaModLoadingContext context)
    {
        IEventBus modEventBus = context.getModEventBus();

        // Register the commonSetup method for modloading
        modEventBus.addListener(this::commonSetup);

        // Register the Deferred Register to the mod event bus so blocks get registered
        BLOCKS.register(modEventBus);
        // Register the Deferred Register to the mod event bus so items get registered
        ITEMS.register(modEventBus);
        // Register the Deferred Register to the mod event bus so tabs get registered
        CREATIVE_MODE_TABS.register(modEventBus);

        // Register ourselves for server and other game events we are interested in
        MinecraftForge.EVENT_BUS.register(this);

        // Register our chaos event handlers
        MinecraftForge.EVENT_BUS.register(new BlockBreakHandler());
        MinecraftForge.EVENT_BUS.register(new MobDropHandler());
        MinecraftForge.EVENT_BUS.register(new FoodEffectHandler());
        MinecraftForge.EVENT_BUS.register(new ToolBehaviorHandler());
        MinecraftForge.EVENT_BUS.register(new CraftingChaosHandler());
        MinecraftForge.EVENT_BUS.register(new WorldChaosHandler());

        // Register the item to a creative tab
        modEventBus.addListener(this::addCreative);

        // Register our mod's ForgeConfigSpec so that Forge can create and load the config file for us
        context.registerConfig(net.minecraftforge.fml.config.ModConfig.Type.COMMON, ModConfig.SPEC);
    }

    private void commonSetup(final FMLCommonSetupEvent event)
    {
        // Some common setup code
        LOGGER.info("CHAOS BEGINS! Survival Unexpected is loading...");
        LOGGER.info("Prepare for the unexpected! Nothing will work as intended!");

        if (ModConfig.enableBlockChaos)
            LOGGER.info("Block chaos is ENABLED - mining will be unpredictable!");

        if (ModConfig.enableMobChaos)
            LOGGER.info("Mob chaos is ENABLED - mob drops will be surprising!");

        if (ModConfig.enableCraftingChaos)
            LOGGER.info("Crafting chaos is ENABLED - recipes will be scrambled!");

        if (ModConfig.enableFoodChaos)
            LOGGER.info("Food chaos is ENABLED - eating will have unexpected effects!");
    }

    // Add our chaos items to the building blocks tab
    private void addCreative(BuildCreativeModeTabContentsEvent event)
    {
        if (event.getTabKey() == CreativeModeTabs.BUILDING_BLOCKS)
            event.accept(CHAOS_BLOCK_ITEM);
    }

    // You can use SubscribeEvent and let the Event Bus discover methods to call
    @SubscribeEvent
    public void onServerStarting(ServerStartingEvent event)
    {
        // Do something when the server starts
        LOGGER.info("Server starting with CHAOS enabled! Expect the unexpected!");
    }

    // You can use EventBusSubscriber to automatically register all static methods in the class annotated with @SubscribeEvent
    @Mod.EventBusSubscriber(modid = MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
    public static class ClientModEvents
    {
        @SubscribeEvent
        public static void onClientSetup(FMLClientSetupEvent event)
        {
            // Some client setup code
            LOGGER.info("Client setup complete - Welcome to the chaos, {}!", Minecraft.getInstance().getUser().getName());
        }
    }
}
