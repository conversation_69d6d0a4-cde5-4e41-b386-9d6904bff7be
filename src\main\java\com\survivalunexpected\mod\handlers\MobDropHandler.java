package com.survivalunexpected.mod.handlers;

import com.survivalunexpected.mod.config.ModConfig;
import com.survivalunexpected.mod.util.ChaosUtil;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.animal.*;
import net.minecraft.world.entity.monster.*;
import net.minecraft.world.entity.npc.Villager;
import net.minecraft.world.entity.ambient.Bat;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraftforge.event.entity.living.LivingDropsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.ArrayList;
import java.util.List;

public class MobDropHandler {

    @SubscribeEvent
    public void onMobDrop(LivingDropsEvent event) {
        if (!ModConfig.enableMobChaos) return;
        if (event.getEntity().level().isClientSide()) return;
        if (!ChaosUtil.shouldChaosOccur()) return;

        LivingEntity entity = event.getEntity();
        Entity damageSource = event.getSource().getEntity();

        if (damageSource instanceof ServerPlayer player) {
            // Clear original drops
            event.getDrops().clear();

            // Generate chaos drops based on mob type
            List<ItemStack> chaosDrops = generateChaosDrops(entity, player);

            // Add chaos drops to the event
            chaosDrops.forEach(stack -> {
                event.getDrops().add(entity.spawnAtLocation(stack));
            });
        }
    }

    private List<ItemStack> generateChaosDrops(LivingEntity entity, ServerPlayer player) {
        List<ItemStack> drops = new ArrayList<>();

        if (isPeacefulMob(entity)) {
            handlePeacefulMobChaos(entity, drops, player);
        } else if (isHostileMob(entity)) {
            handleHostileMobChaos(entity, drops, player);
        } else if (isNeutralMob(entity)) {
            handleNeutralMobChaos(entity, drops, player);
        } else {
            handleGenericMobChaos(entity, drops, player);
        }

        // Add extreme chaos drops if enabled
        if (ModConfig.enableExtremeChaos) {
            addExtremeChaosDrops(entity, drops, player);
        }

        return drops;
    }

    private void handlePeacefulMobChaos(LivingEntity entity, List<ItemStack> drops, ServerPlayer player) {
        if (ModConfig.peacefulMobsDropDangerous) {
            // Peaceful mobs drop dangerous items
            Item dangerousItem = ChaosUtil.getPeacefulMobDangerousDrop();
            drops.add(new ItemStack(dangerousItem, ChaosUtil.getRandomCount(1, 3)));

            if (entity instanceof Cow) {
                drops.add(new ItemStack(Items.TNT, ChaosUtil.getRandomCount(1, 2)));
                ChaosUtil.sendChaosMessage(player, "The cow was hiding explosives! Moo-boom!");
            } else if (entity instanceof Pig) {
                drops.add(new ItemStack(Items.WITHER_SKELETON_SKULL, 1));
                ChaosUtil.sendChaosMessage(player, "The pig carried dark magic! Oink of doom!");
            } else if (entity instanceof Chicken) {
                drops.add(new ItemStack(Items.FIRE_CHARGE, ChaosUtil.getRandomCount(2, 4)));
                ChaosUtil.sendChaosMessage(player, "The chicken was a fire-breathing dragon in disguise!");
            } else if (entity instanceof Sheep) {
                drops.add(new ItemStack(Items.GUNPOWDER, ChaosUtil.getRandomCount(3, 5)));
                ChaosUtil.sendChaosMessage(player, "The sheep was actually a fluffy creeper!");
            } else {
                ChaosUtil.sendChaosMessage(player, "This peaceful creature harbored dangerous secrets!");
            }
        } else {
            // Standard peaceful mob chaos
            drops.add(new ItemStack(ChaosUtil.getRandomItem(), ChaosUtil.getRandomCount(1, 2)));
            ChaosUtil.sendChaosMessage(player, "Even peaceful creatures can surprise you!");
        }
    }

    private void handleHostileMobChaos(LivingEntity entity, List<ItemStack> drops, ServerPlayer player) {
        if (ModConfig.hostileMobsDropHelpful) {
            // Hostile mobs drop helpful items
            Item helpfulItem = ChaosUtil.getHostileMobHelpfulDrop();
            drops.add(new ItemStack(helpfulItem, ChaosUtil.getRandomCount(1, 2)));

            if (entity instanceof Zombie) {
                drops.add(new ItemStack(Items.GOLDEN_APPLE, 1));
                drops.add(new ItemStack(Items.BREAD, ChaosUtil.getRandomCount(2, 4)));
                ChaosUtil.sendChaosMessage(player, "The zombie was just hungry! It brought snacks!");
            } else if (entity instanceof Skeleton) {
                drops.add(new ItemStack(Items.ENCHANTED_BOOK, 1));
                drops.add(new ItemStack(Items.EXPERIENCE_BOTTLE, ChaosUtil.getRandomCount(2, 3)));
                ChaosUtil.sendChaosMessage(player, "The skeleton was a scholar! Knowledge is power!");
            } else if (entity instanceof Creeper) {
                drops.add(new ItemStack(Items.DIAMOND, ChaosUtil.getRandomCount(1, 3)));
                drops.add(new ItemStack(Items.EMERALD, ChaosUtil.getRandomCount(1, 2)));
                ChaosUtil.sendChaosMessage(player, "The creeper was a gem collector! Explosive generosity!");
            } else if (entity instanceof Spider) {
                drops.add(new ItemStack(Items.ELYTRA, 1));
                ChaosUtil.sendChaosMessage(player, "The spider taught you to fly! Eight-legged aviation!");
            } else if (entity instanceof EnderMan) {
                drops.add(new ItemStack(Items.NETHERITE_INGOT, ChaosUtil.getRandomCount(1, 2)));
                ChaosUtil.sendChaosMessage(player, "The Enderman teleported in rare materials!");
            } else if (entity instanceof Witch) {
                drops.add(new ItemStack(Items.TOTEM_OF_UNDYING, 1));
                drops.add(new ItemStack(Items.ENCHANTED_GOLDEN_APPLE, 1));
                ChaosUtil.sendChaosMessage(player, "The witch's final spell was generosity!");
            } else {
                ChaosUtil.sendChaosMessage(player, "This monster had a heart of gold!");
            }
        } else {
            // Standard hostile mob chaos
            drops.add(new ItemStack(ChaosUtil.getRandomItem(), ChaosUtil.getRandomCount(1, 3)));
            ChaosUtil.sendChaosMessage(player, "Even monsters can be unpredictable!");
        }
    }

    private void handleNeutralMobChaos(LivingEntity entity, List<ItemStack> drops, ServerPlayer player) {
        // Neutral mobs get mixed drops
        drops.add(new ItemStack(ChaosUtil.getRandomItem(), ChaosUtil.getRandomCount(1, 2)));

        if (entity instanceof Wolf) {
            drops.add(new ItemStack(Items.BONE, ChaosUtil.getRandomCount(5, 10)));
            drops.add(new ItemStack(Items.STICK, ChaosUtil.getRandomCount(3, 6)));
            ChaosUtil.sendChaosMessage(player, "The wolf brought you fetch supplies!");
        } else if (entity instanceof IronGolem) {
            drops.add(new ItemStack(Items.IRON_BLOCK, ChaosUtil.getRandomCount(2, 4)));
            drops.add(new ItemStack(Items.POPPY, ChaosUtil.getRandomCount(5, 10)));
            ChaosUtil.sendChaosMessage(player, "The iron golem's heart was made of flowers!");
        } else if (entity instanceof Villager) {
            drops.add(new ItemStack(Items.EMERALD, ChaosUtil.getRandomCount(3, 8)));
            drops.add(new ItemStack(Items.BOOK, ChaosUtil.getRandomCount(2, 5)));
            ChaosUtil.sendChaosMessage(player, "The villager shared their knowledge and wealth!");
        } else {
            ChaosUtil.sendChaosMessage(player, "Neutrality brings balance to chaos!");
        }
    }

    private void handleGenericMobChaos(LivingEntity entity, List<ItemStack> drops, ServerPlayer player) {
        // Generic chaos for other mobs
        drops.add(new ItemStack(ChaosUtil.getRandomItem(), ChaosUtil.getRandomCount(1, 2)));
        ChaosUtil.sendChaosMessage(player, "This creature defied all expectations!");
    }

    private void addExtremeChaosDrops(LivingEntity entity, List<ItemStack> drops, ServerPlayer player) {
        if (ChaosUtil.shouldChaosOccur()) {
            // Add completely random items
            for (int i = 0; i < ChaosUtil.getRandomCount(1, 3); i++) {
                drops.add(new ItemStack(ChaosUtil.getRandomItem(), ChaosUtil.getRandomCount(1, 5)));
            }

            // Chance for extremely rare items
            if (ChaosUtil.shouldChaosOccur()) {
                Item[] rareItems = {
                    Items.NETHERITE_BLOCK, Items.BEACON, Items.CONDUIT,
                    Items.DRAGON_EGG, Items.ENCHANTED_GOLDEN_APPLE,
                    Items.NETHER_STAR, Items.HEART_OF_THE_SEA
                };
                Item rareItem = rareItems[ChaosUtil.getRandomCount(0, rareItems.length - 1)];
                drops.add(new ItemStack(rareItem, 1));
                ChaosUtil.sendChaosMessage(player, "EXTREME CHAOS! The universe gifted you something legendary!");
            }
        }
    }

    // Helper methods to categorize mobs
    private boolean isPeacefulMob(LivingEntity entity) {
        return entity instanceof Animal || entity instanceof AgeableMob ||
               entity instanceof WaterAnimal || entity instanceof Squid ||
               entity instanceof Bat || entity instanceof Villager;
    }

    private boolean isHostileMob(LivingEntity entity) {
        return entity instanceof Monster || entity instanceof Enemy ||
               entity instanceof Slime || entity instanceof Ghast ||
               entity instanceof Phantom || entity instanceof Shulker;
    }

    private boolean isNeutralMob(LivingEntity entity) {
        return entity instanceof Wolf || entity instanceof IronGolem ||
               entity instanceof Bee || entity instanceof Dolphin ||
               entity instanceof PolarBear || entity instanceof ZombifiedPiglin;
    }
}
