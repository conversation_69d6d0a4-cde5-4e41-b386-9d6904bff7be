package com.survivalunexpected.mod.handlers;

import com.survivalunexpected.mod.config.ModConfig;
import com.survivalunexpected.mod.util.ChaosUtil;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.chunk.ChunkAccess;
import net.minecraftforge.event.level.ChunkEvent;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.Random;

public class WorldChaosHandler {

    private static final Random RANDOM = new Random();
    private int tickCounter = 0;

    @SubscribeEvent
    public void onChunkLoad(ChunkEvent.Load event) {
        if (!ModConfig.enableWorldChaos) return;
        if (event.getLevel().isClientSide()) return;
        if (!ChaosUtil.shouldChaosOccur()) return;

        if (event.getLevel() instanceof ServerLevel serverLevel) {
            ChunkAccess chunk = event.getChunk();
            scrambleChunkOres(serverLevel, chunk);
        }
    }

    @SubscribeEvent
    public void onWorldTick(TickEvent.LevelTickEvent event) {
        if (!ModConfig.enableWorldChaos) return;
        if (event.level.isClientSide()) return;
        if (event.phase != TickEvent.Phase.END) return;

        tickCounter++;

        // Perform world chaos every 200 ticks (10 seconds)
        if (tickCounter >= 200) {
            tickCounter = 0;
            performRandomWorldChaos((ServerLevel) event.level);
        }
    }

    private void scrambleChunkOres(ServerLevel level, ChunkAccess chunk) {
        if (!ModConfig.scrambleOreGeneration) return;

        BlockPos chunkPos = chunk.getPos().getWorldPosition();

        // Scan through the chunk and replace ores with unexpected blocks
        for (int x = 0; x < 16; x++) {
            for (int z = 0; z < 16; z++) {
                for (int y = level.getMinBuildHeight(); y < level.getMaxBuildHeight(); y++) {
                    BlockPos pos = chunkPos.offset(x, y, z);
                    Block block = level.getBlockState(pos).getBlock();

                    if (isOreBlock(block) && ChaosUtil.shouldChaosOccur()) {
                        Block replacementBlock = getOreReplacementBlock(block);
                        level.setBlock(pos, replacementBlock.defaultBlockState(), 2);
                    }
                }
            }
        }
    }

    private void performRandomWorldChaos(ServerLevel level) {
        if (!ChaosUtil.shouldChaosOccur()) return;

        // Get all players in the level
        for (ServerPlayer player : level.players()) {
            if (ChaosUtil.shouldChaosOccur()) {
                performPlayerAreaChaos(level, player);
            }
        }
    }

    private void performPlayerAreaChaos(ServerLevel level, ServerPlayer player) {
        BlockPos playerPos = player.blockPosition();
        int chaosType = ChaosUtil.getRandomCount(1, 6);

        switch (chaosType) {
            case 1:
                createRandomStructure(level, playerPos, player);
                break;
            case 2:
                spawnRandomBlocks(level, playerPos, player);
                break;
            case 3:
                createFloatingIsland(level, playerPos, player);
                break;
            case 4:
                createUndergroundCavern(level, playerPos, player);
                break;
            case 5:
                transformNearbyBlocks(level, playerPos, player);
                break;
            case 6:
                createChaosPortal(level, playerPos, player);
                break;
        }
    }

    private void createRandomStructure(ServerLevel level, BlockPos center, ServerPlayer player) {
        // Create a random structure near the player
        Block[] structureBlocks = {
            Blocks.DIAMOND_BLOCK, Blocks.EMERALD_BLOCK, Blocks.GOLD_BLOCK,
            Blocks.IRON_BLOCK, Blocks.OBSIDIAN, Blocks.CRYING_OBSIDIAN,
            Blocks.ANCIENT_DEBRIS, Blocks.NETHERITE_BLOCK
        };

        Block buildingBlock = structureBlocks[ChaosUtil.getRandomCount(0, structureBlocks.length - 1)];

        // Build a small pyramid
        int size = ChaosUtil.getRandomCount(3, 7);
        BlockPos buildPos = center.offset(
            ChaosUtil.getRandomCount(-20, 20),
            ChaosUtil.getRandomCount(5, 15),
            ChaosUtil.getRandomCount(-20, 20)
        );

        for (int y = 0; y < size; y++) {
            int currentSize = size - y;
            for (int x = -currentSize; x <= currentSize; x++) {
                for (int z = -currentSize; z <= currentSize; z++) {
                    BlockPos blockPos = buildPos.offset(x, y, z);
                    if (level.getBlockState(blockPos).isAir()) {
                        level.setBlock(blockPos, buildingBlock.defaultBlockState(), 3);
                    }
                }
            }
        }

        ChaosUtil.sendChaosMessage(player, "A mysterious structure appeared! Architectural anomaly!");
    }

    private void spawnRandomBlocks(ServerLevel level, BlockPos center, ServerPlayer player) {
        // Spawn random blocks in the air around the player
        for (int i = 0; i < ChaosUtil.getRandomCount(10, 30); i++) {
            BlockPos randomPos = center.offset(
                ChaosUtil.getRandomCount(-15, 15),
                ChaosUtil.getRandomCount(5, 20),
                ChaosUtil.getRandomCount(-15, 15)
            );

            if (level.getBlockState(randomPos).isAir()) {
                Block randomBlock = getRandomWorldBlock();
                level.setBlock(randomPos, randomBlock.defaultBlockState(), 3);
            }
        }

        ChaosUtil.sendChaosMessage(player, "Blocks rained from the sky! Gravity defied!");
    }

    private void createFloatingIsland(ServerLevel level, BlockPos center, ServerPlayer player) {
        // Create a floating island
        BlockPos islandCenter = center.offset(
            ChaosUtil.getRandomCount(-25, 25),
            ChaosUtil.getRandomCount(20, 40),
            ChaosUtil.getRandomCount(-25, 25)
        );

        int radius = ChaosUtil.getRandomCount(5, 10);

        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {
                for (int y = -2; y <= 1; y++) {
                    double distance = Math.sqrt(x * x + z * z);
                    if (distance <= radius) {
                        BlockPos blockPos = islandCenter.offset(x, y, z);

                        Block blockType;
                        if (y == 1) {
                            blockType = Blocks.GRASS_BLOCK;
                        } else if (y == 0) {
                            blockType = Blocks.DIRT;
                        } else {
                            blockType = Blocks.STONE;
                        }

                        level.setBlock(blockPos, blockType.defaultBlockState(), 3);
                    }
                }
            }
        }

        // Add some trees and decorations
        for (int i = 0; i < ChaosUtil.getRandomCount(3, 8); i++) {
            BlockPos treePos = islandCenter.offset(
                ChaosUtil.getRandomCount(-radius + 2, radius - 2),
                2,
                ChaosUtil.getRandomCount(-radius + 2, radius - 2)
            );

            // Simple tree
            level.setBlock(treePos, Blocks.OAK_LOG.defaultBlockState(), 3);
            level.setBlock(treePos.above(), Blocks.OAK_LOG.defaultBlockState(), 3);
            level.setBlock(treePos.above(2), Blocks.OAK_LEAVES.defaultBlockState(), 3);
            level.setBlock(treePos.above(2).north(), Blocks.OAK_LEAVES.defaultBlockState(), 3);
            level.setBlock(treePos.above(2).south(), Blocks.OAK_LEAVES.defaultBlockState(), 3);
            level.setBlock(treePos.above(2).east(), Blocks.OAK_LEAVES.defaultBlockState(), 3);
            level.setBlock(treePos.above(2).west(), Blocks.OAK_LEAVES.defaultBlockState(), 3);
        }

        ChaosUtil.sendChaosMessage(player, "A floating island materialized! Sky real estate!");
    }

    private void createUndergroundCavern(ServerLevel level, BlockPos center, ServerPlayer player) {
        // Create an underground cavern filled with treasures
        BlockPos cavernCenter = center.offset(
            ChaosUtil.getRandomCount(-20, 20),
            ChaosUtil.getRandomCount(-30, -10),
            ChaosUtil.getRandomCount(-20, 20)
        );

        int radius = ChaosUtil.getRandomCount(8, 15);

        // Hollow out the cavern
        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius/2; y <= radius/2; y++) {
                for (int z = -radius; z <= radius; z++) {
                    double distance = Math.sqrt(x * x + y * y + z * z);
                    if (distance <= radius) {
                        BlockPos blockPos = cavernCenter.offset(x, y, z);
                        level.setBlock(blockPos, Blocks.AIR.defaultBlockState(), 3);
                    }
                }
            }
        }

        // Add treasure blocks
        Block[] treasureBlocks = {
            Blocks.DIAMOND_BLOCK, Blocks.EMERALD_BLOCK, Blocks.GOLD_BLOCK,
            Blocks.ANCIENT_DEBRIS, Blocks.NETHERITE_BLOCK
        };

        for (int i = 0; i < ChaosUtil.getRandomCount(10, 20); i++) {
            BlockPos treasurePos = cavernCenter.offset(
                ChaosUtil.getRandomCount(-radius + 2, radius - 2),
                ChaosUtil.getRandomCount(-radius/2 + 1, radius/2 - 1),
                ChaosUtil.getRandomCount(-radius + 2, radius - 2)
            );

            if (level.getBlockState(treasurePos).isAir()) {
                Block treasureBlock = treasureBlocks[ChaosUtil.getRandomCount(0, treasureBlocks.length - 1)];
                level.setBlock(treasurePos, treasureBlock.defaultBlockState(), 3);
            }
        }

        ChaosUtil.sendChaosMessage(player, "A treasure cavern opened beneath you! Underground riches!");
    }

    private void transformNearbyBlocks(ServerLevel level, BlockPos center, ServerPlayer player) {
        // Transform blocks in the area around the player
        int radius = ChaosUtil.getRandomCount(10, 20);

        for (int x = -radius; x <= radius; x++) {
            for (int y = -5; y <= 5; y++) {
                for (int z = -radius; z <= radius; z++) {
                    if (ChaosUtil.shouldChaosOccur()) {
                        BlockPos blockPos = center.offset(x, y, z);
                        Block currentBlock = level.getBlockState(blockPos).getBlock();

                        if (currentBlock != Blocks.AIR && currentBlock != Blocks.BEDROCK) {
                            Block newBlock = getBlockTransformation(currentBlock);
                            level.setBlock(blockPos, newBlock.defaultBlockState(), 3);
                        }
                    }
                }
            }
        }

        ChaosUtil.sendChaosMessage(player, "The world around you transformed! Reality shift!");
    }

    private void createChaosPortal(ServerLevel level, BlockPos center, ServerPlayer player) {
        // Create a decorative "chaos portal" structure
        BlockPos portalCenter = center.offset(
            ChaosUtil.getRandomCount(-15, 15),
            ChaosUtil.getRandomCount(0, 10),
            ChaosUtil.getRandomCount(-15, 15)
        );

        // Create obsidian frame
        for (int y = 0; y < 4; y++) {
            level.setBlock(portalCenter.offset(-1, y, 0), Blocks.OBSIDIAN.defaultBlockState(), 3);
            level.setBlock(portalCenter.offset(1, y, 0), Blocks.OBSIDIAN.defaultBlockState(), 3);
        }
        for (int x = -1; x <= 1; x++) {
            level.setBlock(portalCenter.offset(x, 0, 0), Blocks.OBSIDIAN.defaultBlockState(), 3);
            level.setBlock(portalCenter.offset(x, 3, 0), Blocks.OBSIDIAN.defaultBlockState(), 3);
        }

        // Fill with crying obsidian for effect
        level.setBlock(portalCenter.offset(0, 1, 0), Blocks.CRYING_OBSIDIAN.defaultBlockState(), 3);
        level.setBlock(portalCenter.offset(0, 2, 0), Blocks.CRYING_OBSIDIAN.defaultBlockState(), 3);

        ChaosUtil.sendChaosMessage(player, "A chaos portal manifested! Gateway to nowhere!");
    }

    private Block getOreReplacementBlock(Block originalOre) {
        if (originalOre == Blocks.DIAMOND_ORE || originalOre == Blocks.DEEPSLATE_DIAMOND_ORE) {
            return Blocks.DIRT;
        } else if (originalOre == Blocks.IRON_ORE || originalOre == Blocks.DEEPSLATE_IRON_ORE) {
            return Blocks.FLOWER_POT;
        } else if (originalOre == Blocks.GOLD_ORE || originalOre == Blocks.DEEPSLATE_GOLD_ORE) {
            return Blocks.COAL_BLOCK;
        } else if (originalOre == Blocks.COAL_ORE || originalOre == Blocks.DEEPSLATE_COAL_ORE) {
            return Blocks.DIAMOND_BLOCK;
        } else {
            return Blocks.STONE;
        }
    }

    private Block getRandomWorldBlock() {
        Block[] worldBlocks = {
            Blocks.STONE, Blocks.DIRT, Blocks.GRASS_BLOCK, Blocks.SAND,
            Blocks.GRAVEL, Blocks.OAK_LOG, Blocks.COBBLESTONE, Blocks.MOSSY_COBBLESTONE,
            Blocks.IRON_ORE, Blocks.COAL_ORE, Blocks.DIAMOND_ORE, Blocks.EMERALD_ORE
        };
        return worldBlocks[ChaosUtil.getRandomCount(0, worldBlocks.length - 1)];
    }

    private Block getBlockTransformation(Block originalBlock) {
        if (originalBlock == Blocks.STONE) return Blocks.DIAMOND_BLOCK;
        if (originalBlock == Blocks.DIRT) return Blocks.EMERALD_BLOCK;
        if (originalBlock == Blocks.GRASS_BLOCK) return Blocks.GOLD_BLOCK;
        if (originalBlock == Blocks.SAND) return Blocks.IRON_BLOCK;
        if (originalBlock == Blocks.WATER) return Blocks.LAVA;
        if (originalBlock == Blocks.LAVA) return Blocks.WATER;
        Item chaosItem = ChaosUtil.getContextualChaosItem(originalBlock.asItem());
        if (chaosItem == Items.DIRT) return Blocks.DIRT;
        if (chaosItem == Items.STONE) return Blocks.STONE;
        if (chaosItem == Items.SAND) return Blocks.SAND;
        return Blocks.COBBLESTONE; // Default fallback
    }

    private boolean isOreBlock(Block block) {
        return block == Blocks.COAL_ORE || block == Blocks.IRON_ORE ||
               block == Blocks.GOLD_ORE || block == Blocks.DIAMOND_ORE ||
               block == Blocks.EMERALD_ORE || block == Blocks.LAPIS_ORE ||
               block == Blocks.REDSTONE_ORE || block == Blocks.COPPER_ORE ||
               block == Blocks.DEEPSLATE_COAL_ORE || block == Blocks.DEEPSLATE_IRON_ORE ||
               block == Blocks.DEEPSLATE_GOLD_ORE || block == Blocks.DEEPSLATE_DIAMOND_ORE ||
               block == Blocks.DEEPSLATE_EMERALD_ORE || block == Blocks.DEEPSLATE_LAPIS_ORE ||
               block == Blocks.DEEPSLATE_REDSTONE_ORE || block == Blocks.DEEPSLATE_COPPER_ORE;
    }
}
