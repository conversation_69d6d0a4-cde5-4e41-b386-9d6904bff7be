# Survival Unexpected - Minecraft Mod

## Overview
Survival Unexpected is a Minecraft mod that completely transforms the survival experience by making every interaction unpredictable and chaotic. Nothing works as expected - mining wood gives you diamonds, crafting tools gives you food, and peaceful mobs drop dangerous items!

## Features

### 🔨 Block Breaking Chaos
- **Wood blocks** drop precious materials like diamonds, emeralds, and food
- **Stone blocks** drop plants, seeds, and organic materials  
- **Ore blocks** drop building materials (diamonds → dirt, iron → flowers)
- **Dirt blocks** reveal hidden treasures and rare items
- **Leaves** forge into tools and weapons
- **Sand** becomes ocean-related items

### 👹 Mob Drop Chaos
- **Peaceful mobs** (cows, pigs, chickens) drop dangerous items like TNT and gunpowder
- **Hostile mobs** (zombies, skeletons, creepers) drop helpful items like golden apples and diamonds
- **Neutral mobs** get mixed drops based on their nature
- Completely randomized drop tables for all creatures

### 🍖 Food Effect Chaos
- **Good foods** (golden apples, bread) now give negative effects
- **Bad foods** (rotten flesh, spider eyes) become beneficial
- **Non-food items** become edible with random effects
- Eating dirt gives earth-related powers, eating iron gives strength

### 🔧 Tool Behavior Chaos
- **Pickaxes** work better on wood than stone
- **Axes** excel at mining stone and ores
- **Shovels** dig through hard materials like butter
- **Tools** have random durability and can transform into other tools
- **Weapons** can break blocks with special effects

### 🔨 Crafting Recipe Chaos
- **Tool recipes** create food instead
- **Food recipes** forge weapons and tools
- **Block recipes** summon rare magical materials
- **Redstone recipes** produce music discs
- Completely scrambled recipe outputs

### 🌍 World Generation Chaos
- **Ore generation** is scrambled (diamond ore becomes dirt)
- **Random structures** appear near players
- **Floating islands** materialize in the sky
- **Underground treasure caverns** open up
- **Chaos portals** manifest randomly
- **Block transformation** events occur periodically

### 🎭 Special Items
- **Mystery Food**: Edible item with completely random effects
- **Chaos Wand**: Magical tool that unleashes unpredictable magic
- **Chaos Block**: Decorative block representing pure chaos

## Configuration Options

The mod includes extensive configuration options:

- **Chaos Chance**: Control how often chaos events occur (0.0 = never, 1.0 = always)
- **Chaos Seed**: Set a specific seed for consistent randomization
- **Individual Toggles**: Enable/disable specific chaos types
- **Extreme Chaos Mode**: Even more unpredictable behavior
- **Chaos Messages**: Toggle informational messages
- **Max Chaos Items**: Control how many items drop from chaos events

## Installation

1. Install Minecraft Forge 1.20.1 (version 47.4.0 or later)
2. Download the Survival Unexpected mod jar file
3. Place the jar file in your `mods` folder
4. Launch Minecraft and enjoy the chaos!

## Compatibility

- **Minecraft Version**: 1.20.1
- **Forge Version**: 47.4.0+
- **Java Version**: 17+

## Development Setup

This mod uses the standard Minecraft Forge development environment:

1. Clone the repository
2. Run `./gradlew genIntellijRuns` (IntelliJ) or `./gradlew genEclipseRuns` (Eclipse)
3. Import the project into your IDE
4. Run the `runClient` configuration to test

## License

This mod is released under the MIT License.

## Credits

Created by the Survival Unexpected Team for players who want to experience Minecraft in a completely new and chaotic way!

## Warning

⚠️ **This mod completely changes core Minecraft mechanics!** ⚠️
- Save your world before installing
- Not recommended for serious survival playthroughs
- May cause confusion, laughter, and unexpected results
- Perfect for players who want a completely fresh Minecraft experience

**Expect the unexpected!**
